// components/dialog.jsx
var Dialog = (props) => {
  function close2() {
    el.remove();
  }
  let el = /* @__PURE__ */ $.h("div", { className: "posF z999 bgc{rgba(48,;48,;48,;0.2)} h100vh w100vw t0 dF aiC jcC" }, /* @__PURE__ */ $.h("div", { className: "bdra20px bgcWhite p32px" }, /* @__PURE__ */ $.h("div", { class: "maxh88vh ovSc" }, props.content), /* @__PURE__ */ $.h("div", { className: "dF jcE" }, /* @__PURE__ */ $.h("div", { className: "AA" }, () => props?.labelClose ? /* @__PURE__ */ $.h("button", { className: "bdN bgcTransparent flR c#1062E5 fs16px mt34px", onClick: () => close2() }, props.labelClose) : null), /* @__PURE__ */ $.h("div", { className: "BB" }, () => {
    return props?.labelYes ? /* @__PURE__ */ $.h("button", { className: "bdN bgcTransparent flR c#1062E5 fs16px mt34px", onClick: () => {
      close2();
      typeof props.exit == "function" && props.exit();
    } }, props.labelYes) : null;
  }))));
  return el;
};

// dw.js
var Dt = Object.create;
var He = Object.defineProperty;
var Ot = Object.getOwnPropertyDescriptor;
var Ct = Object.getOwnPropertyNames;
var Tt = Object.getPrototypeOf;
var Vt = Object.prototype.hasOwnProperty;
var Ft = (n, t) => () => (t || n((t = { exports: {} }).exports, t), t.exports);
var jt = (n, t, e, r) => {
  if (t && typeof t == "object" || typeof t == "function") for (let i of Ct(t)) !Vt.call(n, i) && i !== e && He(n, i, { get: () => t[i], enumerable: !(r = Ot(t, i)) || r.enumerable });
  return n;
};
var zt = (n, t, e) => (e = n != null ? Dt(Tt(n)) : {}, jt(t || !n || !n.__esModule ? He(e, "default", { value: n, enumerable: true }) : e, n));
var At = Ft((ke) => {
  "use strict";
  Object.defineProperty(ke, "__esModule", { value: true });
  ke.Snowflake = void 0;
  var yr = function() {
    function n() {
    }
    return n.generate = function(t) {
      var e = t === void 0 ? {} : t, r = e.timestamp, i = r === void 0 ? Date.now() : r, a = e.shard_id, s = a === void 0 ? n.SHARD_ID : a, o = e.sequence, l = o === void 0 ? Number((Math.random() * 1e4).toFixed(0)) : o;
      i instanceof Date ? i = i.valueOf() : i = new Date(i).valueOf(), s === null && (s = Number((Math.random() * 1e5).toFixed(0)));
      var c = BigInt(i) - BigInt(n.EPOCH) << BigInt(22);
      return c = c | BigInt(s % 1024) << BigInt(12), c = c | BigInt(l % 4096), c.toString();
    }, n.parse = function(t) {
      var e = n.binary(t);
      return { timestamp: n.extractBits(t, 1, 41), shard_id: n.extractBits(t, 42, 10), sequence: n.extractBits(t, 52), binary: e };
    }, n.isValid = function(t) {
      if (!/^[\d]{19}$/.test(t)) return false;
      try {
        return n.parse(t), true;
      } catch {
        return false;
      }
    }, n.extractBits = function(t, e, r) {
      return parseInt(r ? n.binary(t).substring(e, e + r) : n.binary(t).substring(e), 2);
    }, n.binary = function(t) {
      var e = "0000000000000000000000000000000000000000000000000000000000000000", r = BigInt(t).toString(2);
      return r.length < 64 ? e.substring(0, 64 - r.length) + r : r;
    }, n.EPOCH = Date.UTC(1970, 0, 1).valueOf(), n.SHARD_ID = null, n;
  }();
  ke.Snowflake = yr;
});
var { getPrototypeOf: U, fromEntries: Ze, entries: he, keys: Ce, hasOwn: Te } = Object;
var ue;
var Ve;
var ne;
var ce;
var Je = { isConnected: 1 };
var { get: Qe, set: Xe, deleteProperty: Nt, ownKeys: Pt } = Reflect;
var ye;
var Rt = 1e3;
var Bt = {};
var $t = U(Je);
var et = U(U);
var H;
var Fe;
var ee = Symbol();
var tt = Symbol();
var xe = Symbol();
var Q = Symbol();
var fe = Symbol();
var rt = Symbol();
var Sr = Object.getPrototypeOf(async function() {
}).constructor;
var Mt = (n) => (n[tt] = 1, n);
var ie = (n) => n instanceof Object && !(n instanceof Function) && !n[rt];
var nt = (n, t, e, r) => (n ?? (setTimeout(e, r), /* @__PURE__ */ new Set())).add(t);
var it = (n, t, e) => {
  let r = ne;
  ne = t;
  try {
    return n(e);
  } catch {
    return e;
  } finally {
    ne = r;
  }
};
var be = (n) => n?.filter((t) => t._dom?.isConnected) ?? [];
var $e = (n) => ye = nt(ye, n, () => {
  for (let t of ye) t._bindings = be(t._bindings), t._listeners = be(t._listeners);
  ye = H;
}, Rt);
var se = { get value() {
  return ne?._getters?.add(this), this.rawValue;
}, get oldValue() {
  return ne?._getters?.add(this), this._oldValue;
}, set value(n) {
  ne?._setters?.add(this), n !== this.rawValue && (this.rawValue = n, this._bindings.length + this._listeners.length ? (Ve?.add(this), ue = nt(ue, this, Ut)) : this._oldValue = n);
} };
var je = (n) => U(n) === se;
var te = (n) => ({ __proto__: se, rawValue: n, _oldValue: n, _bindings: [], _listeners: [] });
var de = (n, t) => {
  let e = { _getters: /* @__PURE__ */ new Set(), _setters: /* @__PURE__ */ new Set() }, r = { f: n }, i = ce;
  ce = [];
  let a = it(n, e, t);
  a = (a ?? document).nodeType ? a : new Text(a);
  for (let s of e._getters) e._setters.has(s) || ($e(s), s._bindings.push(r));
  for (let s of ce) s._dom = a;
  return ce = i, r._dom = a;
};
var ae = (n, t = te(), e) => {
  let r = { _getters: /* @__PURE__ */ new Set(), _setters: /* @__PURE__ */ new Set() }, i = { f: n, s: t };
  i._dom = e ?? ce?.push(i) ?? Je, t.value = it(n, r, t.rawValue);
  for (let a of r._getters) r._setters.has(a) || ($e(a), a._listeners.push(i));
  return t;
};
var st = (n) => {
  if (n?.[tt]) {
    let t = te();
    return ae(() => {
      let e = n();
      ie(t.rawValue) && ie(e) ? ze(t.rawValue, e) : t.value = we(e);
    }), t;
  } else return te(we(n));
};
var Lt = (n) => {
  let t = Array.isArray(n) ? [] : { __proto__: U(n) };
  for (let [e, r] of he(n)) t[e] = st(r);
  return t[xe] = [], t[Q] = te(1), t;
};
var J = (n = "append") => (t, ...e) => {
  let r = [];
  for (let a of e.flat(1 / 0)) {
    let s = U(a ?? 0), o = s === se ? de(() => a.value) : s === et ? de(a) : a;
    o != H && o.nodeType != 9 && r.push(o);
  }
  return n = { append: "append", prepend: "prepend", before: "before", after: "after", replaceWith: "replaceWith", replaceChildren: "replaceChildren" }[n] ?? "append", t[n](...r), t;
};
var Me = J();
var at = (n, t, ...e) => {
  let [{ is: r, ...i }, ...a] = U(e[0] ?? 0) === $t ? e : [{}, ...e], s = n ? document.createElementNS(n, t, { is: r }) : t == "fragment" ? document.createDocumentFragment() : document.createElement(t, { is: r });
  typeof i?.ref == "function" && i.ref(s);
  for (let [o, l] of Object.entries(i)) {
    if (o === "ref") continue;
    let c = (D) => D ? Object.getOwnPropertyDescriptor(D, o) ?? c(U(D)) : H, h = t + "," + o, f = Bt[h] ??= c(U(s))?.set ?? 0, _ = o.startsWith("on") ? (D, E) => {
      let C = o.slice(2);
      s.removeEventListener(C, E), s.addEventListener(C, D);
    } : f ? f.bind(s) : s.setAttribute.bind(s, o), v = U(l ?? 0);
    o.startsWith("on") || v === et && (l = ae(l), v = se), v === se ? de(() => (_(l.value, l._oldValue), s)) : _(l);
  }
  return Me(s, a);
};
var Ye = (n) => ({ get: (t, e) => at.bind(H, n, e) });
var ot = (n, t) => t ? t !== n && n.replaceWith(t) : n.remove();
var Ut = () => {
  let n = 0, t = [...ue].filter((r) => r.rawValue !== r._oldValue);
  do {
    Ve = /* @__PURE__ */ new Set();
    for (let r of new Set(t.flatMap((i) => i._listeners = be(i._listeners)))) ae(r.f, r.s, r._dom), r._dom = H;
  } while (++n < 100 && (t = [...Ve]).length);
  let e = [...ue].filter((r) => r.rawValue !== r._oldValue);
  ue = H;
  for (let r of new Set(e.flatMap((i) => i._bindings = be(i._bindings)))) ot(r._dom, de(r.f, r._dom)), r._dom = H;
  for (let r of e) r._oldValue = r.rawValue;
};
var lt = { get: (n, t, e) => t === ee ? n : Te(n, t) ? Array.isArray(n) && t === "length" ? n.length : n[t].value : (n[Q].value, Qe(n, t, e)), set: (n, t, e, r) => Te(n, t) ? (Array.isArray(n) && t === "length" ? e !== n.length && (++n[Q].value, n.length = e) : n[t].value = we(e), true) : t in n ? Xe(n, t, e, r) : Xe(n, t, st(e)) ? (++n[Q].value, Le(n).forEach(ut.bind(H, r, t, n[t], Fe)), true) : false, deleteProperty: (n, t) => (Nt(n, t) && Gt(n, t), ++n[Q].value), ownKeys: (n) => (n[Q].value, Pt(n)) };
var ct = (n) => !!(!ie(n) || n[ee]);
var we = (n) => ct(n) ? n : new Proxy(Lt(n), lt);
var It = (n) => (n[rt] = 1, n);
var Kt = (n) => n[ee];
var qt = (n) => new Proxy(n, { get: (t, e, r) => U(t[e] ?? 0) === se ? { value: raw(t[e].rawValue) } : Qe(t, e, r) });
var Wt = (n) => n?.[ee] ? new Proxy(qt(n[ee]), lt) : n;
var Le = (n) => n[xe] = n[xe].filter((t) => t._containerDom.isConnected);
var ut = (n, t, e, r, { _containerDom: i, f: a }) => {
  let s = Array.isArray(n), o = s ? Number(t) : t;
  Me(i, () => i[fe][t] = a(e, () => delete n[t], o)), s && !r && o !== n.length - 1 && i.insertBefore(i.lastChild, i[fe][Ce(n).find((l) => Number(l) > o)]);
};
var Gt = (n, t) => {
  for (let e of Le(n)) {
    let r = e._containerDom[fe];
    r[t]?.remove(), delete r[t];
  }
};
var Ht = (n, t, e) => {
  let r = { _containerDom: n instanceof Function ? n() : n, f: e }, i = t[ee];
  r._containerDom[fe] = {}, i[xe].push(r), $e(i);
  for (let [a, s] of he(i)) ut(t, a, s, 1, r);
  return r._containerDom;
};
var ft = (n, t) => {
  for (let [i, a] of he(t)) {
    let s = n[i];
    ie(s) && ie(a) ? ft(s, a) : n[i] = a;
  }
  for (let i in n) Te(t, i) || delete n[i];
  let e = Ce(t), r = Array.isArray(n);
  if (r || Ce(n).some((i, a) => i !== e[a])) {
    let i = n[ee];
    if (r) n.length = t.length;
    else {
      ++i[Q].value;
      let a = { ...i };
      for (let s of e) delete i[s];
      for (let s of e) i[s] = a[s];
    }
    for (let { _containerDom: a } of Le(i)) {
      let { firstChild: s, [fe]: o } = a;
      for (let l of e) s === o[l] ? s = s.nextSibling : a.insertBefore(o[l], s);
    }
  }
  return n;
};
var ze = (n, t) => {
  Fe = 1;
  try {
    return ft(n, t instanceof Function ? Array.isArray(n) ? t(n.filter((e) => 1)) : Ze(t(he(n))) : t);
  } finally {
    Fe = H;
  }
};
var Ne = (n) => Array.isArray(n) ? n.filter((t) => t !== void 0 && !Number.isNaN(t)).map(Ne) : ie(n) ? Ze(he(n).map(([t, e]) => [t, Ne(e)])) : n;
var Xt = (n, t) => ot(n, de(t, n));
var Pe = new Proxy((n) => new Proxy(at, Ye(n)), Ye());
var Re = (n) => Object.entries(n).reduce((t, e) => t + e[0].split(/(?=[A-Z])/).join("-").toLowerCase() + ":" + e[1] + ";", "");
var Oe = (n, t, e) => {
  if (t === "style") {
    let r = Re(e);
    n.setAttribute(t, r);
    return;
  }
  if (typeof e == "number" && t === "tabIndex") {
    n.setAttribute("tabindex", e.toString());
    return;
  }
  if (typeof e == "string") {
    if (t === "className" || t === "class") {
      n.setAttribute("class", e);
      return;
    }
    if (t === "htmlFor") {
      n.setAttribute("for", e);
      return;
    }
    n.setAttribute(t, e);
    return;
  }
  if (typeof e == "boolean") {
    if (e) {
      n.setAttribute(t, "");
      return;
    }
    n.removeAttribute(t);
  }
};
var Yt = function(n, t, ...e) {
  let r = n, { style: i = null, $ref: a = te(null), ref: s, ...o } = t || {};
  if (typeof r == "string") {
    let l = t?.xmlns ?? null, c = (l ? Pe(l) : Pe)[r](e);
    a.value = c;
    for (let [h, f] of Object.entries(o ?? {})) {
      if (typeof f == "function" && !h.startsWith("on")) {
        ae(() => {
          let _ = f();
          Oe(c, h, _);
        });
        continue;
      }
      if (typeof f == "function" && h.startsWith("on")) {
        c.addEventListener(h.replace("on", "").toLowerCase(), f);
        continue;
      }
      if (typeof f == "object" && "value" in f) {
        ae(() => {
          Oe(c, h, f.value);
        });
        continue;
      }
      Oe(c, h, f);
    }
    return s && (typeof s == "function" ? constructor.name == "AsyncFunction" ? (async () => await s(c))() : s(c) : typeof s == "object" && je(s) && (s.value = c)), i && c.setAttribute("style", Re(i)), c;
  }
  if (typeof r == "function") {
    let l = null;
    return r.toString().startsWith("class") ? l = new r({ $ref: a, ...o, children: e })?.renderDom() : l = r({ $ref: a, ...o, children: e }), a.value = l, l && (i && l.setAttribute("style", Re(i)), s && (typeof s == "function" ? r.constructor.name == "AsyncFunction" ? (async () => await s(l))() : s(l) : typeof s == "object" && je(s) && (s.value = l))), l;
  }
  return null;
};
var Zt = function({ children: n }) {
  let t = document.createDocumentFragment();
  return n.forEach((e) => {
    t.appendChild(e);
  }), t;
};
var Be = class {
  constructor(t) {
    this.props = t, this.$ref = t.$ref ?? te(null), this.init();
  }
  init() {
  }
  mount(t) {
  }
  render() {
  }
  renderDom() {
    let t = this.render();
    return this.$ref.value = t, setTimeout(() => {
      this.mount(t);
    }), t;
  }
};
var F = { tags: Pe, hydrate: Xt, insertNodes: J, append: Me, prepend: J("prepend"), before: J("before"), after: J("after"), replaceWith: J("replaceWith"), replaceChildren: J("replaceChildren"), stateValue: te, derive: ae, calcFnc: Mt, isStateValue: je, isStateObject: ct, stateObject: we, noStateObject: It, stateFields: Kt, rawObject: Wt, updateStateObject: ze, replaceState: ze, listObjectDom: Ht, compact: Ne, h: Yt, f: Zt, jsxc: Be };
var Jt = Object.prototype.hasOwnProperty;
var B = "~";
function pe() {
}
Object.create && (pe.prototype = /* @__PURE__ */ Object.create(null), new pe().__proto__ || (B = false));
function Qt(n, t, e) {
  this.fn = n, this.context = t, this.once = e || false;
}
function dt(n, t, e, r, i) {
  if (typeof e != "function") throw new TypeError("The listener must be a function");
  var a = new Qt(e, r || n, i), s = B ? B + t : t;
  return n._events[s] ? n._events[s].fn ? n._events[s] = [n._events[s], a] : n._events[s].push(a) : (n._events[s] = a, n._eventsCount++), n;
}
function ve(n, t) {
  --n._eventsCount === 0 ? n._events = new pe() : delete n._events[t];
}
function R() {
  this._events = new pe(), this._eventsCount = 0;
}
R.prototype.eventNames = function() {
  var t = [], e, r;
  if (this._eventsCount === 0) return t;
  for (r in e = this._events) Jt.call(e, r) && t.push(B ? r.slice(1) : r);
  return Object.getOwnPropertySymbols ? t.concat(Object.getOwnPropertySymbols(e)) : t;
};
R.prototype.listeners = function(t) {
  var e = B ? B + t : t, r = this._events[e];
  if (!r) return [];
  if (r.fn) return [r.fn];
  for (var i = 0, a = r.length, s = new Array(a); i < a; i++) s[i] = r[i].fn;
  return s;
};
R.prototype.listenerCount = function(t) {
  var e = B ? B + t : t, r = this._events[e];
  return r ? r.fn ? 1 : r.length : 0;
};
R.prototype.emit = function(t, e, r, i, a, s) {
  var o = B ? B + t : t;
  if (!this._events[o]) return false;
  var l = this._events[o], c = arguments.length, h, f;
  if (l.fn) {
    switch (l.once && this.removeListener(t, l.fn, void 0, true), c) {
      case 1:
        return l.fn.call(l.context), true;
      case 2:
        return l.fn.call(l.context, e), true;
      case 3:
        return l.fn.call(l.context, e, r), true;
      case 4:
        return l.fn.call(l.context, e, r, i), true;
      case 5:
        return l.fn.call(l.context, e, r, i, a), true;
      case 6:
        return l.fn.call(l.context, e, r, i, a, s), true;
    }
    for (f = 1, h = new Array(c - 1); f < c; f++) h[f - 1] = arguments[f];
    l.fn.apply(l.context, h);
  } else {
    var _ = l.length, v;
    for (f = 0; f < _; f++) switch (l[f].once && this.removeListener(t, l[f].fn, void 0, true), c) {
      case 1:
        l[f].fn.call(l[f].context);
        break;
      case 2:
        l[f].fn.call(l[f].context, e);
        break;
      case 3:
        l[f].fn.call(l[f].context, e, r);
        break;
      case 4:
        l[f].fn.call(l[f].context, e, r, i);
        break;
      default:
        if (!h) for (v = 1, h = new Array(c - 1); v < c; v++) h[v - 1] = arguments[v];
        l[f].fn.apply(l[f].context, h);
    }
  }
  return true;
};
R.prototype.on = function(t, e, r) {
  return dt(this, t, e, r, false);
};
R.prototype.once = function(t, e, r) {
  return dt(this, t, e, r, true);
};
R.prototype.removeListener = function(t, e, r, i) {
  var a = B ? B + t : t;
  if (!this._events[a]) return this;
  if (!e) return ve(this, a), this;
  var s = this._events[a];
  if (s.fn) s.fn === e && (!i || s.once) && (!r || s.context === r) && ve(this, a);
  else {
    for (var o = 0, l = [], c = s.length; o < c; o++) (s[o].fn !== e || i && !s[o].once || r && s[o].context !== r) && l.push(s[o]);
    l.length ? this._events[a] = l.length === 1 ? l[0] : l : ve(this, a);
  }
  return this;
};
R.prototype.removeAllListeners = function(t) {
  var e;
  return t ? (e = B ? B + t : t, this._events[e] && ve(this, e)) : (this._events = new pe(), this._eventsCount = 0), this;
};
R.prototype.off = R.prototype.removeListener;
R.prototype.addListener = R.prototype.on;
R.prefixed = B;
R.EventEmitter = R;
var ht = R;
var er = ["html,body,*{ font-family: 'Roboto', sans-serif;font-style: normal;line-height: 1.4;max-width:inherit;max-height:inherit;overflow:inherit;}", "html[x-css-reset]{display: flex; min-height: 100%; flex-direction: column;}", "[x-app] * {display: none;}", "[x-css-reset] ::before, [x-css-reset] ::after { box-sizing: border-box;  border-width: 0; border-style: solid; }", "[x-css-reset] :-moz-focusring {outline: auto;}", "[x-css-reset] :-moz-ui-invalid { box-shadow: none;}", "[x-css-reset] :disabled {cursor: default;}", "[x-css-reset] ::-webkit-inner-spin-button,[x-css-reset] ::-webkit-outer-spin-button { height: auto;}", "[x-css-reset] ::-webkit-search-decoration {-webkit-appearance: none;}", "[x-css-reset] ::-webkit-file-upload-button {-webkit-appearance: button;font: inherit;}", '[x-css-reset] [type="search"] { outline-offset: -2px;}', "[x-css-reset] [hidden] {display: none;}", "[x-css-reset] hr { height: 0; color: inherit; border-top-width: 1px;}", "[x-css-reset] abbr:where([title]) { text-decoration: underline dotted; }", "[x-css-reset] a {color: inherit; text-decoration: inherit; }", "[x-css-reset] b,[x-css-reset] strong {font-weight: bolder; }", "[x-css-reset] small {font-size: 80%; }", "[x-css-reset] sub,[x-css-reset] sup {font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }", "[x-css-reset] sub {bottom: -0.25em;}", "[x-css-reset] sup {top: -0.5em;}", "[x-css-reset] table { text-indent: 0; border-color: inherit; border-collapse: collapse;}", "[x-css-reset]  button,[x-css-reset] input,[x-css-reset] optgroup,[x-css-reset] select,[x-css-reset] textarea {font-family: inherit;font-feature-settings: inherit;font-variation-settings: inherit;font-size: 100%;font-weight: inherit;line-height: inherit;color: inherit;margin: 0;padding: 0;}", "[x-css-reset]  button,[x-css-reset] select { text-transform: none;}", '[x-css-reset] button,[x-css-reset] [type="button"],[x-css-reset] [type="reset"],[x-css-reset] [type="submit"] {background-color: transparent;background-image: none;}', '[x-css-reset] button,[x-css-reset] [role="button"] {cursor: pointer;}', "[x-css-reset] progress {vertical-align: baseline;}", "[x-css-reset] blockquote,[x-css-reset] dl,[x-css-reset] dd,[x-css-reset] h1,[x-css-reset] h2,[x-css-reset] h3,[x-css-reset] h4,[x-css-reset] h5,[x-css-reset] h6,[x-css-reset] hr,[x-css-reset] figure,[x-css-reset] p,[x-css-reset] pre {margin: 0;}", "[x-css-reset] fieldset {margin: 0;padding: 0;}", "[x-css-reset] legend {padding: 0;}", "[x-css-reset] ol,[x-css-reset] ul,[x-css-reset] menu {list-style: none;margin: 0;padding: 0;}", "[x-css-reset] dialog {padding: 0;}", "[x-css-reset] textarea {resize: vertical;}", "[x-css-reset] input::placeholder,[x-css-reset] textarea::placeholder {opacity: 1;color: #9ca3af;}", "[x-css-reset] summary {display: list-item;}", "[x-css-reset] img,[x-css-reset] svg,[x-css-reset] video,[x-css-reset] canvas,[x-css-reset] audio,[x-css-reset] iframe,[x-css-reset] embed,[x-css-reset] object {display: block}", "[x-css-reset] img,[x-css-reset] video {max-width: 100%;height: auto;}", "[x-css-reset] form{margin-block-end: unset;}", "[x-css-reset] h1,[x-css-reset] h2,[x-css-reset] h3,[x-css-reset] h4,[x-css-reset] h5,[x-css-reset] h6{color: var(--c-black);font-weight: 700;line-height: 1.5;margin: 0;padding: 0; }", "[x-css-reset] h1{ font-size: var(--fs-4xl); }", "[x-css-reset] h2{  font-size: var(--fs-3xl);}", "[x-css-reset] h3{ font-size: var(--fs-2xl); }", "[x-css-reset] h4{ font-size: var(--fs-xl);}", "[x-css-reset] h5{ font-size: var(--fs-lg);}", "[x-css-reset] h6{  font-size: var(--fs-m); }", "[x-css-reset]  button { font-size: var(--fs-m); }", "input.TMA,select.TMA,textarea.TMA{border:1px solid #D9D9D9;}", "input.TMA,select.TMA,textarea.TMA{ padding: 0.65rem 1rem; border-radius: 0.3rem;}", `select.TMA{ -webkit-appearance: none;-moz-appearance: none;appearance: none; min-width:80px; background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"/></svg>') no-repeat right 8px center; background-size: 12px;background-color:#fff;}`, "button.TMA{white-space:nowrap; border-radius: 5px;margin:auto 8px;padding: 0.5rem 1rem; font-size: 1rem; background-color: transparent;cursor: pointer;}", "button.TMA2{white-space:nowrap; border-radius: 5px;margin:auto 8px;padding: 4px 8px; font-size: 1rem; background-color: transparent;cursor: pointer;}", "button.Btn1{ border: 1px solid #004fad; color: #004fad;}", "button.Btn2{border: none; background-color: #004fad;color: #FFFFFF;}", "button.Btn3{ border: 1px solid rgb(255, 0, 0); color: rgb(255, 0, 0);}", "button.Btn4{border: 1px solid rgb(255, 0, 0);background-color:rgb(255, 0, 0);color: #FFFFFF;}", "button:disabled.Btn1{ border-color:#004fadb3; background-color: #f3f3f3;}", "button:disabled.Btn2{background-color:#004fadb3}", "td button.TMA{padding: 4px 8px;}", ".Row.TMA{margin:8px auto;}", ".Icon {font-family: 'icon'; font-weight: normal;font-style: normal; font-size: 24px;line-height: 1;letter-spacing: normal;text-transform: none;display: inline-block;white-space: nowrap; word-wrap: normal; direction: ltr; -webkit-font-smoothing: antialiased;}"];
var tr = "@layer l0{" + er.join("") + "}";
var Se = tr;
var Ue = [];
var rr = [];
var Ae = {};
var nr = { c: "color", fs: "font-size", ff: "font-family", fw: "font-weight", fst: "font-style", ta: "text-align", td: "text-decoration", lh: "line-height", ls: "letter-spacing", ws: "word-spacing", va: "vertical-align", bg: "background", bgc: "background-color", bgi: "background-image", bgp: "background-position", bgr: "background-repeat", bgs: "background-size", bgo: "background-origin", bgcl: "background-clip", bga: "background-attachment", o: "opacity", d: "display", pos: "position", t: "top", r: "right", b: "bottom", l: "left", fl: "float", cl: "clear", z: "z-index", w: "width", h: "height", minw: "min-width", minh: "min-height", maxw: "max-width", maxh: "max-height", m: "margin", mt: "margin-top", mr: "margin-right", mb: "margin-bottom", ml: "margin-left", p: "padding", pt: "padding-top", pr: "padding-right", pb: "padding-bottom", pl: "padding-left", bd: "border", bdt: "border-top", bdr: "border-right", bdb: "border-bottom", bdl: "border-left", bdw: "border-width", bds: "border-style", bdc: "border-color", bdra: "border-radius", fx: "flex", fxd: "flex-direction", fxw: "flex-wrap", jc: "justify-content", ai: "align-items", ac: "align-content", as: "align-self", fxg: "flex-grow", fxs: "flex-shrink", fxb: "flex-basis", or: "order", gtc: "grid-template-columns", gtr: "grid-template-rows", gac: "grid-auto-columns", gar: "grid-auto-rows", gc: "grid-column", gr: "grid-row", g: "gap", tf: "transform", ts: "transition", bxs: "box-sizing", bxsh: "box-shadow", ft: "filter", an: "animation", cr: "cursor", ov: "overflow", v: "visibility", pte: "pointer-events" };
var ir = { t: "transparent", cc: "currentColor", i: "inherit", a: "auto", n: "none", s: "start", e: "end", c: "center", ba: "baseline", st: "stretch", sb: "space-between", sa: "space-around", ss: "stretch", b: "block", in: "inline", inb: "inline-block", f: "flex", g: "grid", l: "left", r: "right", j: "justify", u: "underline", ovl: "overline", lt: "line-through", sc: "scroll", h: "hidden", v: "visible", nrep: "no-repeat", rep: "repeat", repx: "repeat-x", repy: "repeat-y", repn: "repeat no-repeat" };
var sr = () => {
  let n = window?.$?.cssModules ?? {};
  return { pos: { s: "static", r: "relative", a: "absolute", f: "fixed" }, fxd: { c: "column", cr: "column-reverse", r: "row", rr: "row-reverse" }, fxw: { n: "nowrap", w: "wrap", wr: "wrap-reverse" }, ...n };
};
var gt = (n, t) => {
  let e = 0, r = n.length - 1;
  for (; e <= r; ) {
    let i = Math.floor((e + r) / 2);
    if (n[i] === t) return i;
    n[i] < t ? e = i + 1 : r = i - 1;
  }
  return -1;
};
var pt = (n, t) => {
  if (n.sort(), gt(n, t) !== -1) return false;
  let r = 0;
  for (; r < n.length && n[r] < t; ) r++;
  return n.splice(r, 0, t), true;
};
var mt = (n, t) => gt(n, t) !== -1;
var Ie = (n) => {
  let t = /^((?<m>xs|sm|md|lg|xl|2xl):)?(?<l>\d*)?(?<p>((([a-z])((-)?[a-z])*)(([A-Z#!])|((-)?[0-9])|(--)|({))([^&@]+)?)(&(([a-z])((-)?[a-z])*)(([A-Z#!])|((-)?[0-9])|(--)|({))([^@]+)?)?)(@(?<s>.*))?$/;
  if (mt(Ue, n) || mt(rr, n)) return null;
  let { m: e = "default", l: r = 0, p: i, s: a = "" } = t.exec(n)?.groups ?? {};
  if (!i) return pt(Ue, n), null;
  let s = i.split("&").map((f) => ar(f)).filter((f) => f), o = a.replace(/;/g, " "), l = CSS.escape(n), c = `selector(${l}${o})`;
  return o && !CSS.supports(c) ? (pt(Ue, n), null) : { media: e, layer: r, className: l, property: s.join(";"), selector: o };
};
var ar = (n) => {
  let t = /^(?<p>([a-z])((-)?[a-z])*)(?<v>(([A-Z#!])|((-)?[0-9])|(--)|({))(.+)?)$/, { p: e, v: r } = t.exec(n)?.groups ?? {};
  if (!e || !r) return null;
  let i = e.split("-"), a = i.shift(), s = i.join("-"), o = nr[a], l = o ? o + (s ? "-" + s : "") : e, c = r[0], h = r.substring(1), f = c !== "!" ? r : h, _ = c == "!";
  f = f[0].toLowerCase() + f.substring(1);
  let v = sr();
  f.startsWith("--") ? f = "var(" + f + ")" : f.startsWith("{") && f.endsWith("}") ? f = f.substring(1, f.length - 1) : v[e] ? f = v[e]?.[f] ?? f : f = ir[f] || f, f = f.replace(/;/g, " ");
  let D = f + (_ ? " !important" : ""), E = [l + ":" + D];
  switch (l) {
    case "mx":
      E = [`margin-left:${D}`, `margin-right:${D}`];
      break;
    case "my":
      E = [`margin-top:${D}`, `margin-bottom:${D}`];
      break;
    case "px":
      E = [`padding-left:${D}`, `padding-right:${D}`];
      break;
    case "py":
      E = [`padding-top:${D}`, `padding-bottom:${D}`];
      break;
    case "bdx":
      E = [`border-left:${D}`, `border-right:${D}`];
      break;
    case "bdy":
      E = [`border-top:${D}`, `border-bottom:${D}`];
      break;
  }
  return E.every((C) => CSS.supports(C)) ? E.join(";") : null;
};
function or(n) {
  var t = /class="([^"]+)"/g, e = [];
  return [...n.matchAll(t)].forEach((r) => e = e.concat(r[1].split(" "))), [...new Set(e)];
}
function yt(n = []) {
  for (let r of n) {
    var t = [...r?.classList || []];
    if (t.length > 0) {
      var e = [];
      let i = t.filter((s) => s && !(s.charCodeAt() >= 65 && s.charCodeAt() <= 90)), a = t.filter((s) => !i.includes(s));
      if (i.length > 0) {
        let s = [];
        i.forEach((o) => {
          if (!Ae[o]) {
            let l = cr(3);
            s.push(l), Ae[o] = l;
          }
        }), i = i.map((o) => Ae[o]), e = [...a, ...s];
      }
    }
    if (r.children.length > 0) {
      let i = yt(r.children);
    }
  }
  return Object.keys(Ae);
}
function _e(n) {
  return n ? yt([n]) : [];
}
function xt(n, t) {
  if (typeof t != "function") throw new Error("Callback is not a function");
  if (!n) return;
  let e = null;
  n instanceof Document ? e = n.documentElement : (n instanceof ShadowRoot || n instanceof HTMLElement) && (e = n), e && (t(_e(n)), new MutationObserver((r) => {
    for (let i of r) if (i.type == "attributes" && i.attributeName == "class") {
      if (i.target.nodeType == 1) {
        let a = i.target.className || "", s = i.oldValue || "", o = a.split(" ").map((c) => c.trim()).filter((c) => c), l = s.split(" ").map((c) => c.trim()).filter((c) => c);
        o = o.filter((c) => !l.includes(c)), typeof t == "function" && t([...new Set(o)]);
      }
    } else if (i.type == "childList" && i.addedNodes.length > 0) {
      let a = [...i.addedNodes].filter((s) => s.nodeType == 1).map((s) => _e(s)).flat(1 / 0).map((s) => s.trim()).filter((s) => s);
      typeof t == "function" && t([...new Set(a)]);
    }
  }).observe(e, { attributes: true, attributeOldValue: true, attributeFilter: ["class"], childList: true, subtree: true }));
}
function bt(n) {
  let t = Ie(n);
  if (!t) return null;
  let { media: e, layer: r, property: i, selector: a, className: s } = t;
  return { layer: r, media: e, className: `.${s}${a}`, property: i };
}
function lr(n) {
  let t = { default: { media: "", data: /* @__PURE__ */ new Set() }, xs: { media: "screen and (max-width: 575px)", data: /* @__PURE__ */ new Set() }, sm: { media: "screen and (min-width: 576px)", data: /* @__PURE__ */ new Set() }, md: { media: "screen and (min-width: 768px)", data: /* @__PURE__ */ new Set() }, lg: { media: "screen and (min-width: 992px)", data: /* @__PURE__ */ new Set() }, xl: { media: "screen and (min-width: 1200px)", data: /* @__PURE__ */ new Set() }, "2xl": { media: "screen and (min-width: 1400px)", data: /* @__PURE__ */ new Set() } };
  _e(n).forEach((r) => {
    let i = bt(r), a = i.className;
    var s = `@layer l${i.layer}{${a}{${i.property}}}`;
    t[i.media].data.add(s);
  });
  let e = {};
  return Object.keys(t).forEach((r) => {
    e[r] = { meta: t[r].media, data: [...t[r].data] };
  }), JSON.stringify(e);
}
function cr(n = 1) {
  let t = "", e = Date.now().toString(32).slice(6).toUpperCase();
  for (let i = 0; i < n; i++) t += String.fromCharCode(Math.floor(Math.random() * 25 + 65));
  return String.fromCharCode(Math.floor(Math.random() * 25 + 65)) + e + t;
}
function ur(n = document, t) {
  let e = n?.getRootNode();
  if (!e) return;
  let r = [], i = new ht(), a = { default: /* @__PURE__ */ new Set(), xs: /* @__PURE__ */ new Set(), sm: /* @__PURE__ */ new Set(), md: /* @__PURE__ */ new Set(), lg: /* @__PURE__ */ new Set(), xl: /* @__PURE__ */ new Set(), "2xl": /* @__PURE__ */ new Set() }, s = { root: new CSSStyleSheet(), default: new CSSStyleSheet(), xs: new CSSStyleSheet({ media: "screen and (max-width: 575px)" }), sm: new CSSStyleSheet({ media: "screen and (min-width: 576px)" }), md: new CSSStyleSheet({ media: "screen and (min-width: 768px)" }), lg: new CSSStyleSheet({ media: "screen and (min-width: 992px)" }), xl: new CSSStyleSheet({ media: "screen and (min-width: 1200px)" }), "2xl": new CSSStyleSheet({ media: "screen and (min-width: 1400px)" }) };
  var o = () => {
    let c;
    return (h) => {
      clearTimeout(c), c = setTimeout(() => {
        t && Object.keys(a).forEach((f) => {
          let _ = t + "-" + f;
          a[f].size > 0 && localStorage?.setItem(_, JSON.stringify([...a[f]]));
        });
      }, h || 0);
    };
  }, l = () => {
    t && Object.keys(a).forEach((c) => {
      let h = t + "-" + c, f = localStorage?.getItem(h) ?? null;
      f && (a[c] = new Set(JSON.parse(f)), s[c].replaceSync([...a[c]].join(" ")));
    });
  };
  l(), Se && Array.isArray(Se) && s.root.replaceSync(Se.join(`
`)), Object.keys(s).forEach((c) => {
    e.adoptedStyleSheets = [...e.adoptedStyleSheets, s[c]];
  }), i.on("change", (c) => {
    let { media: h, property: f, selector: _, layer: v, className: D } = c, E = s[h], C = a[h];
    v = (_ ? 1 : 0) + v;
    var P = `@layer l${v}{.${D}${_}{${f}}}`;
    C.has(P) || (C.add(P), E.insertRule(P));
  }), xt(e, (c) => {
    c.map((f) => Ie(f)).filter((f) => f).forEach((f) => {
      r.includes(f.className) || (r.push(f.className), i.emit("change", f));
    });
  });
}
var Ke = { convertToClass: Ie, getAllClassFromString: or, getAllClassFromDom: _e, eventDom: xt, storeCss: ur, generateCssString: bt, generateAllCSS: lr };
var fr = 0;
var { button: Tr, div: $2, header: dr, input: Vr, label: Fr, span: hr, style: jr } = F.tags;
var Y = (n) => Object.entries(n).map(([t, e]) => `${t}: ${e};`).join("");
var pr = Object.getPrototypeOf(F.stateValue(null));
var me = (n) => Object.getPrototypeOf(n ?? 0) === pr ? n : F.stateValue(n);
var qe = ({ value: n, container: t = $2, Loading: e, Error: r, options: i = {} }, a) => {
  let s = F.stateValue({ status: "pending" });
  return n.then((o) => {
    s.value = { status: "fulfilled", value: o };
  }).catch((o) => s.value = { status: "rejected", value: o }), t(i, () => s.value.status === "pending" ? e?.() ?? "" : s.value.status === "rejected" ? r?.(s.value.value) : typeof a == "function" ? a(s.value.value) : s.value.value);
};
var mr = ({ value: n, container: t = $2, Loading: e, Error: r, options: i = {}, props: a = {} }) => {
  let s = fetch(n).then((o) => o.text());
  return qe({ value: s, container: t, Loading: e, Error: r, options: i }, (o) => {
    let l = F.stateValue(""), h = F.htmlStringToFw(o, true).map((f) => {
      let _ = Object.keys(a), v = _.length > 0 ? new Function(..._, "props", "return " + f.trim())(..._.map((D) => a[D]), a) : new Function("props", "return " + f.trim())(a);
      return typeof v == "function" ? v() : v;
    });
    return l.value = $2(h), l.value;
  });
};
var Ee = () => ++fr;
var We = ({ title: n, closed: t = F.stateValue(false), x: e = 0, y: r = 0, width: i = 300, height: a = "auto", closeCross: s = "\xD7", cb: o = () => {
}, init: l = {}, customStacking: c = false, zIndex: h = 1, disableMove: f = false, disableResize: _ = false, headerColor: v = "lightgray", windowClass: D = "", windowStyleOverrides: E = {}, headerClass: C = "", headerStyleOverrides: I = {}, childrenContainerClass: P = "", childrenContainerStyleOverrides: L = {}, crossClass: K = "", crossStyleOverrides: X = {}, crossHoverClass: M = "", crossHoverStyleOverrides: m = {} }, ...k) => {
  e = typeof e == "number" ? e : 0, r = typeof r == "number" ? r : 0;
  let { width: w, height: S } = document.body.getBoundingClientRect();
  e = e == 0 ? w / 2 : e, r = r == 0 ? S / 2 : r;
  let T = me(e), u = me(r), d = me(i), p = me(a), g = me(h);
  c || (g.value = Ee());
  let x = F.stateValue(false), y = F.stateValue(null), b = F.stateValue(0), O = F.stateValue(0), A = F.stateValue(0), V = F.stateValue(0), z = M || Object.keys(m) ? F.stateValue(false) : null, N = (j) => {
    j.button === 0 && (x.value = true, b.value = j.clientX, O.value = j.clientY);
  }, Z = (j) => (W) => {
    y.value = j, b.value = W.clientX, O.value = W.clientY, A.value = d.value, V.value = p.value;
  }, q = (j) => {
    if (x.value) T.value += j.clientX - b.value, u.value += j.clientY - O.value, b.value = j.clientX, O.value = j.clientY;
    else if (y.value) {
      let W = j.clientX - b.value, ge = j.clientY - O.value;
      y.value.includes("right") && typeof d.value == "number" && (d.value = A.value + W), y.value.includes("bottom") && typeof p.value == "number" && (p.value = V.value + ge);
    }
  }, oe = () => {
    x.value = false, y.value = null;
  };
  document.addEventListener("mousemove", q), document.addEventListener("mouseup", oe);
  let le = F.stateValue(null);
  return () => t.value ? null : $2({ class: "posF w100% h100% bgc{rgba(0,0,0,0.5)} t0", style: () => Y({ "z-index": g.value }) }, $2({ ref: (j) => le.value = j, class: ["posF bgcWhite bxshd{0px;1px;4px;3px;#5252521f} bda{0.5rem}"].concat(D || []).join(" "), style: () => {
    let { width: j, height: W } = document.body.getBoundingClientRect();
    return Y({ left: `${T.value * 100 / j}%`, top: `${u.value * 100 / W}%`, width: typeof d.value == "number" ? `${d.value}px` : d.value, height: typeof p.value == "number" ? `${p.value}px` : p.value, transform: `translate(-${T.value * 100 / j}%, -${u.value * 100 / W}%)`, ...E });
  }, ...c ? {} : { onmousedown: () => g.value = Ee() } }, n ? dr({ class: ["crMo usN dF jcSp aiC"].concat(C || []).join(" "), style: Y({ "background-color": v, ...f ? { cursor: "auto" } : {}, ...I }), ...f ? {} : { onmousedown: N } }, n, s ? hr({ class: () => ["cr tran{background-color;0.3s,color;0.3s} bda{50%} w24px h24px dF aiC jcC bgcRed:hover cWhite:hover"].concat(K || []).concat(M && z.value ? M : []).join(" "), style: () => Y({ ...X, ...Object.keys(m).length && z.value ? m : {} }), onclick: () => t.value = true, ...z ? { onmouseenter: () => z.value = true, onmouseleave: () => z.value = false } : {} }, s) : null) : f ? null : $2({ class: "crMo posA l0px t0px w100% h1rem", onmousedown: N }), _ ? [] : [$2({ class: "crEr posA r0px t0px w10px h100% bgc{transparent}", onmousedown: Z("right") }), $2({ class: "crSr posA l0px b0px w100% h10px bgc{transparent}", onmousedown: Z("bottom") }), $2({ class: "crSer posA r0px b0px w10px h10px bgc{transparent}", onmousedown: Z("rightbottom") })], $2({ class: () => "mh{calc(100vh;-;100px)} " + ["ofl{auto}"].concat(P || []).join(" "), style: Y(L) }, k)));
};
var gr = (n, t) => {
  F.appendDom(document.body, We(n, t));
};
var Ge = class {
  _boardStylesStr;
  _fadeOutSec;
  _messageClass;
  _messageStylesStr;
  _closerClass;
  _closerStylesStr;
  _dom;
  _options;
  constructor({ top: t = "unset", bottom: e = "unset", left: r = "unset", right: i = "unset", backgroundColor: a = "white", fontColor: s = "black", fadeOutSec: o = 0.3, boardClass: l = "", boardStyleOverrides: c = {}, messageClass: h = "", messageStyleOverrides: f = {}, closerClass: _ = "", closerStyleOverrides: v = {} }, D = document.body) {
    r == "unset" && (r = i === "unset" ? "50%" : "unset"), i == "unset" && (i = r === "unset" ? "50%" : "unset"), t == "unset" && (t = e === "unset" ? "50%" : "unset"), e == "unset" && (e = t === "unset" ? "50%" : "unset"), this._options = { top: t, bottom: e, left: r, right: i, backgroundColor: a, fontColor: s, fadeOutSec: o, boardClass: l, boardStyleOverrides: c, messageClass: h, messageStyleOverrides: f, closerClass: _, closerStyleOverrides: v };
    let E = Y({ display: "flex", "flex-direction": "column", "align-items": "center", position: "fixed", top: t, bottom: e, left: r, right: i, "z-index": 1e4, ...c });
    this._boardStylesStr = E, this._fadeOutSec = o, this._messageClass = h, this._messageStylesStr = Y({ display: "flex", "background-color": a, color: s, padding: "15px", "margin-bottom": "10px", "border-radius": "5px", "box-shadow": "0px 1px 4px 3px #00000038", transition: `opacity ${o}s, transform ${o}s`, ...f }), this._closerClass = _, this._closerStylesStr = Y({ display: "flex", "align-items": "center", "margin-left": "10px", cursor: "pointer", ...v }), this._dom = $2({ class: l, style: E }), F.appendDom(D, () => this._dom);
  }
  show({ message: t, closer: e = "\xD7", messageClass: r = "", closerClass: i = "", postion: a = {}, durationSec: s = 3, closed: o = F.stateValue(false), expiredCb: l }) {
    a = { ...this._options, ...a }, this._dom.style.top = a.top, this._dom.style.bottom = a.bottom, this._dom.style.left = a.left, this._dom.style.right = a.right;
    let c = null, h = F.stateValue(false);
    F.derive(() => setTimeout((_) => {
      h.value = _, _ && c && clearTimeout(c);
    }, this._fadeOutSec * 1e3, o.value));
    let f = $2({ class: () => "bda5px bxshd{1px;1px;4px;#00000061} dF gap{10px} p10px mw450px " + r }, $2(t), e ? $2({ class: () => "cRed cr fns18px fw700 " + i, onclick: () => o.value = true }, e) : null);
    return F.derive(() => o.value && (f.style.opacity = "0", f.style.transform = "translateY(-20px)")), typeof s == "number" && s > 0 && (c = setTimeout(() => {
      o.value = true, typeof l == "function" && l();
    }, s * 1e3)), F.appendDom(this._dom, () => h.value ? null : f), o;
  }
  remove() {
    this._dom.remove();
  }
};
var wt = { HtmlRender: mr, ElementSync: qe, topIndex: Ee, dialog: gr, ElementDialog: We, Await: qe, topMostZIndex: Ee, FloatingWindow: We, MessageBoard: Ge };
var vt = (() => {
  "use strict";
  var n = 5960464477539063e-23, t = 4294967296, e = 9007199254740992;
  function r(a) {
    var s = new ArrayBuffer(256), o = new DataView(s), l, c = 0;
    function h(m) {
      for (var k = s.byteLength, w = c + m; k < w; ) k <<= 1;
      if (k !== s.byteLength) {
        var S = o;
        s = new ArrayBuffer(k), o = new DataView(s);
        for (var T = c + 3 >> 2, u = 0; u < T; ++u) o.setUint32(u << 2, S.getUint32(u << 2));
      }
      return l = m, o;
    }
    function f() {
      c += l;
    }
    function _(m) {
      f(h(8).setFloat64(c, m));
    }
    function v(m) {
      f(h(1).setUint8(c, m));
    }
    function D(m) {
      for (var k = h(m.length), w = 0; w < m.length; ++w) k.setUint8(c + w, m[w]);
      f();
    }
    function E(m) {
      f(h(2).setUint16(c, m));
    }
    function C(m) {
      f(h(4).setUint32(c, m));
    }
    function I(m) {
      var k = m % t, w = (m - k) / t, S = h(8);
      S.setUint32(c, w), S.setUint32(c + 4, k), f();
    }
    function P(m, k) {
      k < 24 ? v(m << 5 | k) : k < 256 ? (v(m << 5 | 24), v(k)) : k < 65536 ? (v(m << 5 | 25), E(k)) : k < 4294967296 ? (v(m << 5 | 26), C(k)) : (v(m << 5 | 27), I(k));
    }
    function L(m) {
      var k;
      if (m === false) return v(244);
      if (m === true) return v(245);
      if (m === null) return v(246);
      if (m === void 0) return v(247);
      switch (typeof m) {
        case "number":
          if (Math.floor(m) === m) {
            if (0 <= m && m <= e) return P(0, m);
            if (-e <= m && m < 0) return P(1, -(m + 1));
          }
          return v(251), _(m);
        case "string":
          var w = [];
          for (k = 0; k < m.length; ++k) {
            var S = m.charCodeAt(k);
            S < 128 ? w.push(S) : S < 2048 ? (w.push(192 | S >> 6), w.push(128 | S & 63)) : S < 55296 ? (w.push(224 | S >> 12), w.push(128 | S >> 6 & 63), w.push(128 | S & 63)) : (S = (S & 1023) << 10, S |= m.charCodeAt(++k) & 1023, S += 65536, w.push(240 | S >> 18), w.push(128 | S >> 12 & 63), w.push(128 | S >> 6 & 63), w.push(128 | S & 63));
          }
          return P(3, w.length), D(w);
        default:
          var T;
          if (Array.isArray(m)) for (T = m.length, P(4, T), k = 0; k < T; ++k) L(m[k]);
          else if (m instanceof Uint8Array) P(2, m.length), D(m);
          else {
            var u = Object.keys(m);
            for (T = u.length, P(5, T), k = 0; k < T; ++k) {
              var d = u[k];
              L(d), L(m[d]);
            }
          }
      }
    }
    if (L(a), "slice" in s) return s.slice(0, c);
    for (var K = new ArrayBuffer(c), X = new DataView(K), M = 0; M < c; ++M) X.setUint8(M, o.getUint8(M));
    return K;
  }
  function i(a, s, o) {
    var l = new DataView(a), c = 0;
    typeof s != "function" && (s = function(w) {
      return w;
    }), typeof o != "function" && (o = function() {
    });
    function h(w, S) {
      return c += w, S;
    }
    function f(w) {
      return h(w, new Uint8Array(a, c, w));
    }
    function _() {
      var w = new ArrayBuffer(4), S = new DataView(w), T = C(), u = T & 32768, d = T & 31744, p = T & 1023;
      if (d === 31744) d = 261120;
      else if (d !== 0) d += 114688;
      else if (p !== 0) return (u ? -1 : 1) * p * n;
      return S.setUint32(0, u << 16 | d << 13 | p << 13), S.getFloat32(0);
    }
    function v() {
      return h(4, l.getFloat32(c));
    }
    function D() {
      return h(8, l.getFloat64(c));
    }
    function E() {
      return h(1, l.getUint8(c));
    }
    function C() {
      return h(2, l.getUint16(c));
    }
    function I() {
      return h(4, l.getUint32(c));
    }
    function P() {
      return I() * t + I();
    }
    function L() {
      return l.getUint8(c) !== 255 ? false : (c += 1, true);
    }
    function K(w) {
      if (w < 24) return w;
      if (w === 24) return E();
      if (w === 25) return C();
      if (w === 26) return I();
      if (w === 27) return P();
      if (w === 31) return -1;
      throw "Invalid length encoding";
    }
    function X(w) {
      var S = E();
      if (S === 255) return -1;
      var T = K(S & 31);
      if (T < 0 || S >> 5 !== w) throw "Invalid indefinite length element";
      return T;
    }
    function M(w, S) {
      for (var T = 0; T < S; ++T) {
        var u = E();
        u & 128 && (u < 224 ? (u = (u & 31) << 6 | E() & 63, S -= 1) : u < 240 ? (u = (u & 15) << 12 | (E() & 63) << 6 | E() & 63, S -= 2) : (u = (u & 15) << 18 | (E() & 63) << 12 | (E() & 63) << 6 | E() & 63, S -= 3)), u < 65536 ? w.push(u) : (u -= 65536, w.push(55296 | u >> 10), w.push(56320 | u & 1023));
      }
    }
    function m() {
      var w = E(), S = w >> 5, T = w & 31, u, d;
      if (S === 7) switch (T) {
        case 25:
          return _();
        case 26:
          return v();
        case 27:
          return D();
      }
      if (d = K(T), d < 0 && (S < 2 || 6 < S)) throw "Invalid length";
      switch (S) {
        case 0:
          return d;
        case 1:
          return -1 - d;
        case 2:
          if (d < 0) {
            for (var p = [], g = 0; (d = X(S)) >= 0; ) g += d, p.push(f(d));
            var x = new Uint8Array(g), y = 0;
            for (u = 0; u < p.length; ++u) x.set(p[u], y), y += p[u].length;
            return x;
          }
          return f(d);
        case 3:
          var b = [];
          if (d < 0) for (; (d = X(S)) >= 0; ) M(b, d);
          else M(b, d);
          return String.fromCharCode.apply(null, b);
        case 4:
          var O;
          if (d < 0) for (O = []; !L(); ) O.push(m());
          else for (O = new Array(d), u = 0; u < d; ++u) O[u] = m();
          return O;
        case 5:
          var A = {};
          for (u = 0; u < d || d < 0 && !L(); ++u) {
            var V = m();
            A[V] = m();
          }
          return A;
        case 6:
          return s(m(), d);
        case 7:
          switch (d) {
            case 20:
              return false;
            case 21:
              return true;
            case 22:
              return null;
            case 23:
              return;
            default:
              return o(d);
          }
      }
    }
    var k = m();
    if (c !== a.byteLength) throw "Remaining bytes";
    return k;
  }
  return { encode: r, decode: i };
})();
var St = function() {
  "use strict";
  var n = function(e, r, i) {
    i = t.extend({}, t.options, i);
    var a = t.runValidations(e, r, i), s, o;
    if (a.some(function(l) {
      return t.isPromise(l.error);
    })) throw new Error("Use validate.async if you want support for promises");
    return n.processValidationResults(a, i);
  }, t = n;
  return t.extend = function(e) {
    return [].slice.call(arguments, 1).forEach(function(r) {
      for (var i in r) e[i] = r[i];
    }), e;
  }, t.extend(n, { version: { major: 0, minor: 13, patch: 1, metadata: null, toString: function() {
    var e = t.format("%{major}.%{minor}.%{patch}", t.version);
    return t.isEmpty(t.version.metadata) || (e += "+" + t.version.metadata), e;
  } }, Promise: typeof Promise < "u" ? Promise : null, EMPTY_STRING_REGEXP: /^\s*$/, runValidations: function(e, r, i) {
    var a = [], s, o, l, c, h, f, _;
    (t.isDomElement(e) || t.isJqueryElement(e)) && (e = t.collectFormValues(e));
    for (s in r) {
      l = t.getDeepObjectValue(e, s), c = t.result(r[s], l, e, s, i, r);
      for (o in c) {
        if (h = t.validators[o], !h) throw _ = t.format("Unknown validator %{name}", { name: o }), new Error(_);
        f = c[o], f = t.result(f, l, e, s, i, r), f && a.push({ attribute: s, value: l, validator: o, globalOptions: i, attributes: e, options: f, error: h.call(h, l, f, s, e, i) });
      }
    }
    return a;
  }, processValidationResults: function(e, r) {
    e = t.pruneEmptyErrors(e, r), e = t.expandMultipleErrors(e, r), e = t.convertErrorMessages(e, r);
    var i = r.format || "grouped";
    if (typeof t.formatters[i] == "function") e = t.formatters[i](e);
    else throw new Error(t.format("Unknown format %{format}", r));
    return t.isEmpty(e) ? void 0 : e;
  }, async: function(e, r, i) {
    i = t.extend({}, t.async.options, i);
    var a = i.wrapErrors || function(o) {
      return o;
    };
    i.cleanAttributes !== false && (e = t.cleanAttributes(e, r));
    var s = t.runValidations(e, r, i);
    return new t.Promise(function(o, l) {
      t.waitForResults(s).then(function() {
        var c = t.processValidationResults(s, i);
        c ? l(new a(c, i, e, r)) : o(e);
      }, function(c) {
        l(c);
      });
    });
  }, single: function(e, r, i) {
    return i = t.extend({}, t.single.options, i, { format: "flat", fullMessages: false }), t({ single: e }, { single: r }, i);
  }, waitForResults: function(e) {
    return e.reduce(function(r, i) {
      return t.isPromise(i.error) ? r.then(function() {
        return i.error.then(function(a) {
          i.error = a || null;
        });
      }) : r;
    }, new t.Promise(function(r) {
      r();
    }));
  }, result: function(e) {
    var r = [].slice.call(arguments, 1);
    return typeof e == "function" && (e = e.apply(null, r)), e;
  }, isNumber: function(e) {
    return typeof e == "number" && !isNaN(e);
  }, isFunction: function(e) {
    return typeof e == "function";
  }, isInteger: function(e) {
    return t.isNumber(e) && e % 1 === 0;
  }, isBoolean: function(e) {
    return typeof e == "boolean";
  }, isObject: function(e) {
    return e === Object(e);
  }, isDate: function(e) {
    return e instanceof Date;
  }, isDefined: function(e) {
    return e != null;
  }, isPromise: function(e) {
    return !!e && t.isFunction(e.then);
  }, isJqueryElement: function(e) {
    return e && t.isString(e.jquery);
  }, isDomElement: function(e) {
    return !e || !e.querySelectorAll || !e.querySelector ? false : t.isObject(document) && e === document ? true : typeof HTMLElement == "object" ? e instanceof HTMLElement : e && typeof e == "object" && e !== null && e.nodeType === 1 && typeof e.nodeName == "string";
  }, isEmpty: function(e) {
    var r;
    if (!t.isDefined(e)) return true;
    if (t.isFunction(e)) return false;
    if (t.isString(e)) return t.EMPTY_STRING_REGEXP.test(e);
    if (t.isArray(e)) return e.length === 0;
    if (t.isDate(e)) return false;
    if (t.isObject(e)) {
      for (r in e) return false;
      return true;
    }
    return false;
  }, format: t.extend(function(e, r) {
    return t.isString(e) ? e.replace(t.format.FORMAT_REGEXP, function(i, a, s) {
      return a === "%" ? "%{" + s + "}" : String(r[s]);
    }) : e;
  }, { FORMAT_REGEXP: /(%?)%\{([^\}]+)\}/g }), prettify: function(e) {
    return t.isNumber(e) ? e * 100 % 1 === 0 ? "" + e : parseFloat(Math.round(e * 100) / 100).toFixed(2) : t.isArray(e) ? e.map(function(r) {
      return t.prettify(r);
    }).join(", ") : t.isObject(e) ? t.isDefined(e.toString) ? e.toString() : JSON.stringify(e) : (e = "" + e, e.replace(/([^\s])\.([^\s])/g, "$1 $2").replace(/\\+/g, "").replace(/[_-]/g, " ").replace(/([a-z])([A-Z])/g, function(r, i, a) {
      return "" + i + " " + a.toLowerCase();
    }).toLowerCase());
  }, stringifyValue: function(e, r) {
    var i = r && r.prettify || t.prettify;
    return i(e);
  }, isString: function(e) {
    return typeof e == "string";
  }, isArray: function(e) {
    return {}.toString.call(e) === "[object Array]";
  }, isHash: function(e) {
    return t.isObject(e) && !t.isArray(e) && !t.isFunction(e);
  }, contains: function(e, r) {
    return t.isDefined(e) ? t.isArray(e) ? e.indexOf(r) !== -1 : r in e : false;
  }, unique: function(e) {
    return t.isArray(e) ? e.filter(function(r, i, a) {
      return a.indexOf(r) == i;
    }) : e;
  }, forEachKeyInKeypath: function(e, r, i) {
    if (t.isString(r)) {
      var a = "", s, o = false;
      for (s = 0; s < r.length; ++s) switch (r[s]) {
        case ".":
          o ? (o = false, a += ".") : (e = i(e, a, false), a = "");
          break;
        case "\\":
          o ? (o = false, a += "\\") : o = true;
          break;
        default:
          o = false, a += r[s];
          break;
      }
      return i(e, a, true);
    }
  }, getDeepObjectValue: function(e, r) {
    if (t.isObject(e)) return t.forEachKeyInKeypath(e, r, function(i, a) {
      if (t.isObject(i)) return i[a];
    });
  }, collectFormValues: function(e, r) {
    var i = {}, a, s, o, l, c, h;
    if (t.isJqueryElement(e) && (e = e[0]), !e) return i;
    for (r = r || {}, l = e.querySelectorAll("input[name], textarea[name]"), a = 0; a < l.length; ++a) if (o = l.item(a), !t.isDefined(o.getAttribute("data-ignored"))) {
      var f = o.name.replace(/\./g, "\\\\.");
      h = t.sanitizeFormValue(o.value, r), o.type === "number" ? h = h ? +h : null : o.type === "checkbox" ? o.attributes.value ? o.checked || (h = i[f] || null) : h = o.checked : o.type === "radio" && (o.checked || (h = i[f] || null)), i[f] = h;
    }
    for (l = e.querySelectorAll("select[name]"), a = 0; a < l.length; ++a) if (o = l.item(a), !t.isDefined(o.getAttribute("data-ignored"))) {
      if (o.multiple) {
        h = [];
        for (s in o.options) c = o.options[s], c && c.selected && h.push(t.sanitizeFormValue(c.value, r));
      } else {
        var _ = typeof o.options[o.selectedIndex] < "u" ? o.options[o.selectedIndex].value : "";
        h = t.sanitizeFormValue(_, r);
      }
      i[o.name] = h;
    }
    return i;
  }, sanitizeFormValue: function(e, r) {
    return r.trim && t.isString(e) && (e = e.trim()), r.nullify !== false && e === "" ? null : e;
  }, capitalize: function(e) {
    return t.isString(e) ? e[0].toUpperCase() + e.slice(1) : e;
  }, pruneEmptyErrors: function(e) {
    return e.filter(function(r) {
      return !t.isEmpty(r.error);
    });
  }, expandMultipleErrors: function(e) {
    var r = [];
    return e.forEach(function(i) {
      t.isArray(i.error) ? i.error.forEach(function(a) {
        r.push(t.extend({}, i, { error: a }));
      }) : r.push(i);
    }), r;
  }, convertErrorMessages: function(e, r) {
    r = r || {};
    var i = [], a = r.prettify || t.prettify;
    return e.forEach(function(s) {
      var o = t.result(s.error, s.value, s.attribute, s.options, s.attributes, s.globalOptions);
      if (!t.isString(o)) {
        i.push(s);
        return;
      }
      o[0] === "^" ? o = o.slice(1) : r.fullMessages !== false && (o = t.capitalize(a(s.attribute)) + " " + o), o = o.replace(/\\\^/g, "^"), o = t.format(o, { value: t.stringifyValue(s.value, r) }), i.push(t.extend({}, s, { error: o }));
    }), i;
  }, groupErrorsByAttribute: function(e) {
    var r = {};
    return e.forEach(function(i) {
      var a = r[i.attribute];
      a ? a.push(i) : r[i.attribute] = [i];
    }), r;
  }, flattenErrorsToArray: function(e) {
    return e.map(function(r) {
      return r.error;
    }).filter(function(r, i, a) {
      return a.indexOf(r) === i;
    });
  }, cleanAttributes: function(e, r) {
    function i(o, l, c) {
      return t.isObject(o[l]) ? o[l] : o[l] = c ? true : {};
    }
    function a(o) {
      var l = {}, c, h;
      for (h in o) o[h] && t.forEachKeyInKeypath(l, h, i);
      return l;
    }
    function s(o, l) {
      if (!t.isObject(o)) return o;
      var c = t.extend({}, o), h, f;
      for (f in o) h = l[f], t.isObject(h) ? c[f] = s(c[f], h) : h || delete c[f];
      return c;
    }
    return !t.isObject(r) || !t.isObject(e) ? {} : (r = a(r), s(e, r));
  }, exposeModule: function(e, r, i, a, s) {
    i ? (a && a.exports && (i = a.exports = e), i.validate = e) : (r.validate = e, e.isFunction(s) && s.amd && s([], function() {
      return e;
    }));
  }, warn: function(e) {
  }, error: function(e) {
  } }), n.validators = { valid: function(e, r) {
    if (r = t.extend({}, this.options, r), typeof r.checkValid == "function") {
      let i = r.checkValid(e);
      if (i) return r.message || this.message || i;
    }
  }, presence: function(e, r) {
    if (r = t.extend({}, this.options, r), r.allowEmpty !== false ? !t.isDefined(e) : t.isEmpty(e)) return r.message || this.message || "can't be blank";
  }, length: function(e, r, i) {
    if (t.isDefined(e)) {
      r = t.extend({}, this.options, r);
      var a = r.is, s = r.maximum, o = r.minimum, l = r.tokenizer || function(_) {
        return _;
      }, c, h = [];
      e = l(e);
      var f = e.length;
      if (!t.isNumber(f)) return r.message || this.notValid || "has an incorrect length";
      if (t.isNumber(a) && f !== a && (c = r.wrongLength || this.wrongLength || "is the wrong length (should be %{count} characters)", h.push(t.format(c, { count: a }))), t.isNumber(o) && f < o && (c = r.tooShort || this.tooShort || "is too short (minimum is %{count} characters)", h.push(t.format(c, { count: o }))), t.isNumber(s) && f > s && (c = r.tooLong || this.tooLong || "is too long (maximum is %{count} characters)", h.push(t.format(c, { count: s }))), h.length > 0) return r.message || h;
    }
  }, numericality: function(e, r, i, a, s) {
    if (t.isDefined(e)) {
      r = t.extend({}, this.options, r);
      var o = [], l, c, h = { greaterThan: function(E, C) {
        return E > C;
      }, greaterThanOrEqualTo: function(E, C) {
        return E >= C;
      }, equalTo: function(E, C) {
        return E === C;
      }, lessThan: function(E, C) {
        return E < C;
      }, lessThanOrEqualTo: function(E, C) {
        return E <= C;
      }, divisibleBy: function(E, C) {
        return E % C === 0;
      } }, f = r.prettify || s && s.prettify || t.prettify;
      if (t.isString(e) && r.strict) {
        var _ = "^-?(0|[1-9]\\d*)";
        if (r.onlyInteger || (_ += "(\\.\\d+)?"), _ += "$", !new RegExp(_).test(e)) return r.message || r.notValid || this.notValid || this.message || "must be a valid number";
      }
      if (r.noStrings !== true && t.isString(e) && !t.isEmpty(e) && (e = +e), !t.isNumber(e)) return r.message || r.notValid || this.notValid || this.message || "is not a number";
      if (r.onlyInteger && !t.isInteger(e)) return r.message || r.notInteger || this.notInteger || this.message || "must be an integer";
      for (l in h) if (c = r[l], t.isNumber(c) && !h[l](e, c)) {
        var v = "not" + t.capitalize(l), D = r[v] || this[v] || this.message || "must be %{type} %{count}";
        o.push(t.format(D, { count: c, type: f(l) }));
      }
      if (r.odd && e % 2 !== 1 && o.push(r.notOdd || this.notOdd || this.message || "must be odd"), r.even && e % 2 !== 0 && o.push(r.notEven || this.notEven || this.message || "must be even"), o.length) return r.message || o;
    }
  }, datetime: t.extend(function(e, r) {
    if (!t.isFunction(this.parse) || !t.isFunction(this.format)) throw new Error("Both the parse and format functions needs to be set to use the datetime/date validator");
    if (t.isDefined(e)) {
      r = t.extend({}, this.options, r);
      var i, a = [], s = r.earliest ? this.parse(r.earliest, r) : NaN, o = r.latest ? this.parse(r.latest, r) : NaN;
      if (e = this.parse(e, r), isNaN(e) || r.dateOnly && e % 864e5 !== 0) return i = r.notValid || r.message || this.notValid || "must be a valid date", t.format(i, { value: arguments[0] });
      if (!isNaN(s) && e < s && (i = r.tooEarly || r.message || this.tooEarly || "must be no earlier than %{date}", i = t.format(i, { value: this.format(e, r), date: this.format(s, r) }), a.push(i)), !isNaN(o) && e > o && (i = r.tooLate || r.message || this.tooLate || "must be no later than %{date}", i = t.format(i, { date: this.format(o, r), value: this.format(e, r) }), a.push(i)), a.length) return t.unique(a);
    }
  }, { parse: null, format: null }), date: function(e, r) {
    return r = t.extend({}, r, { dateOnly: true }), t.validators.datetime.call(t.validators.datetime, e, r);
  }, format: function(e, r) {
    (t.isString(r) || r instanceof RegExp) && (r = { pattern: r }), r = t.extend({}, this.options, r);
    var i = r.message || this.message || "is invalid", a = r.pattern, s;
    if (t.isDefined(e) && (!t.isString(e) || (t.isString(a) && (a = new RegExp(r.pattern, r.flags)), s = a.exec(e), !s || s[0].length != e.length))) return i;
  }, inclusion: function(e, r) {
    if (t.isDefined(e) && (t.isArray(r) && (r = { within: r }), r = t.extend({}, this.options, r), !t.contains(r.within, e))) {
      var i = r.message || this.message || "^%{value} is not included in the list";
      return t.format(i, { value: e });
    }
  }, exclusion: function(e, r) {
    if (t.isDefined(e) && (t.isArray(r) && (r = { within: r }), r = t.extend({}, this.options, r), !!t.contains(r.within, e))) {
      var i = r.message || this.message || "^%{value} is restricted";
      return t.isString(r.within[e]) && (e = r.within[e]), t.format(i, { value: e });
    }
  }, email: t.extend(function(e, r) {
    r = t.extend({}, this.options, r);
    var i = r.message || this.message || "is not a valid email";
    if (t.isDefined(e) && (!t.isString(e) || !this.PATTERN.exec(e))) return i;
  }, { PATTERN: /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i }), equality: function(e, r, i, a, s) {
    if (t.isDefined(e)) {
      t.isString(r) && (r = { attribute: r }), r = t.extend({}, this.options, r);
      var o = r.message || this.message || "is not equal to %{attribute}";
      if (t.isEmpty(r.attribute) || !t.isString(r.attribute)) throw new Error("The attribute must be a non empty string");
      var l = t.getDeepObjectValue(a, r.attribute), c = r.comparator || function(f, _) {
        return f === _;
      }, h = r.prettify || s && s.prettify || t.prettify;
      if (!c(e, l, r, i, a)) return t.format(o, { attribute: h(r.attribute) });
    }
  }, url: function(e, r) {
    if (t.isDefined(e)) {
      r = t.extend({}, this.options, r);
      var i = r.message || this.message || "is not a valid url", a = r.schemes || this.schemes || ["http", "https"], s = r.allowLocal || this.allowLocal || false, o = r.allowDataUrl || this.allowDataUrl || false;
      if (!t.isString(e)) return i;
      var l = "^(?:(?:" + a.join("|") + ")://)(?:\\S+(?::\\S*)?@)?(?:", c = "(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))";
      if (s ? c += "?" : l += "(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})", l += "(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*" + c + ")(?::\\d{2,5})?(?:[/?#]\\S*)?$", o) {
        var h = "\\w+\\/[-+.\\w]+(?:;[\\w=]+)*", f = "[A-Za-z0-9-_.!~\\*'();\\/?:@&=+$,%]*", _ = "data:(?:" + h + ")?(?:;base64)?," + f;
        l = "(?:" + l + ")|(?:^" + _ + "$)";
      }
      var v = new RegExp(l, "i");
      if (!v.exec(e)) return i;
    }
  }, type: t.extend(function(e, r, i, a, s) {
    if (t.isString(r) && (r = { type: r }), !!t.isDefined(e)) {
      var o = t.extend({}, this.options, r), l = o.type;
      if (!t.isDefined(l)) throw new Error("No type was specified");
      var c;
      if (t.isFunction(l) ? c = l : c = this.types[l], !t.isFunction(c)) throw new Error("validate.validators.type.types." + l + " must be a function.");
      if (!c(e, o, i, a, s)) {
        var h = r.message || this.messages[l] || this.message || o.message || (t.isFunction(l) ? "must be of the correct type" : "must be of type %{type}");
        return t.isFunction(h) && (h = h(e, r, i, a, s)), t.format(h, { attribute: t.prettify(i), type: l });
      }
    }
  }, { types: { object: function(e) {
    return t.isObject(e) && !t.isArray(e);
  }, array: t.isArray, integer: t.isInteger, number: t.isNumber, string: t.isString, date: t.isDate, boolean: t.isBoolean }, messages: {} }) }, n.formatters = { detailed: function(e) {
    return e;
  }, flat: t.flattenErrorsToArray, grouped: function(e) {
    var r;
    e = t.groupErrorsByAttribute(e);
    for (r in e) e[r] = t.flattenErrorsToArray(e[r]);
    return e;
  }, constraint: function(e) {
    var r;
    e = t.groupErrorsByAttribute(e);
    for (r in e) e[r] = e[r].map(function(i) {
      return i.validator;
    }).sort();
    return e;
  } }, n;
}();
var _t = zt(At());
var re = class {
  constructor(t, e) {
    this.size = Math.ceil(-t * Math.log(e) / Math.pow(Math.log(2), 2)), this.hashCount = Math.ceil(this.size / t * Math.log(2)), this.bitArray = new Uint8Array(this.size), this.len = 0;
  }
  async hash(t, e) {
    let i = new TextEncoder().encode(e + t), a = await crypto.subtle.digest("SHA-256", i);
    return Array.from(new Uint8Array(a)).reduce((l, c) => l << 8 | c, 0) % this.size;
  }
  async add(t) {
    if (!await this.contains(t)) {
      if (this.len >= this.size) throw new Error("Bloom Filter is full");
      this.len++;
    }
    for (let e = 0; e < this.hashCount; e++) {
      let r = await this.hash(t, e);
      this.bitArray[r] = 1;
    }
  }
  async contains(t) {
    for (let e = 0; e < this.hashCount; e++) {
      let r = await this.hash(t, e);
      if (this.bitArray[r] === 0) return false;
    }
    return true;
  }
};
var Et = (() => {
  function n(u) {
    let d = "";
    for (let p = 0; p < u.length; p++) d += String.fromCharCode(u[p]);
    return d;
  }
  function t(u) {
    return new TextEncoder().encode(u);
  }
  function e(u) {
    let d = n(new Uint8Array(u));
    return i(d);
  }
  function r(u) {
    let d = a(u), p = new Uint8Array(d.length);
    for (let g = 0; g < d.length; g++) p[g] = d.charCodeAt(g);
    return p;
  }
  function i(u) {
    return btoa(u);
  }
  function a(u) {
    return atob(u);
  }
  function s(u) {
    let d = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", p = "", g = "", x = [];
    for (let b = 0; b < u.length; b++) x.push(u.charCodeAt(b));
    for (let b = 0; b < x.length; b += 3) {
      let O = x[b], A = x[b + 1] || 0, V = x[b + 2] || 0, z = O << 16 | A << 8 | V;
      p += d[z >> 18 & 63], p += d[z >> 12 & 63], p += d[z >> 6 & 63], p += d[z & 63];
    }
    let y = (3 - x.length % 3) % 3;
    return y && (g = "=".repeat(y), p = p.slice(0, p.length - y) + g), p;
  }
  function o(u) {
    let d = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", p = [], g = u.replace(/=/g, "");
    for (let y = 0; y < g.length; y += 4) {
      let b = g.slice(y, y + 4), O = d.indexOf(b[0]), A = d.indexOf(b[1]), V = b[2] ? d.indexOf(b[2]) : 0, z = b[3] ? d.indexOf(b[3]) : 0, N = O << 18 | A << 12 | V << 6 | z;
      p.push(N >> 16 & 255), b[2] && p.push(N >> 8 & 255), b[3] && p.push(N & 255);
    }
    let x = "";
    for (let y = 0; y < p.length; y++) x += String.fromCharCode(p[y]);
    return x;
  }
  async function l(u) {
    let d = new Response(u).body.pipeThrough(new CompressionStream("gzip")), p = await new Response(d).arrayBuffer(), g = new Uint8Array(p);
    return e(g);
  }
  async function c(u) {
    let d = r(u), p = new Response(d).body.pipeThrough(new DecompressionStream("gzip"));
    return new Uint8Array(await new Response(p).arrayBuffer());
  }
  async function h(u) {
    let d = await c(u);
    return new TextDecoder().decode(d);
  }
  async function f(u, d) {
    let p = await v(d), g = p.substring(0, 32), x = p.substring(48, 60), y = t(g), b = t(x), O = t(u), A = await window.crypto.subtle.importKey("raw", y, { name: "AES-GCM" }, false, ["encrypt"]), V = await window.crypto.subtle.encrypt({ name: "AES-GCM", iv: b }, A, O);
    return e(V);
  }
  async function _(u, d) {
    let p = await v(d), g = p.substring(0, 32), x = p.substring(48, 60), y = t(g), b = t(x), O = r(u), A = await window.crypto.subtle.importKey("raw", y, { name: "AES-GCM" }, false, ["decrypt"]), V = await window.crypto.subtle.decrypt({ name: "AES-GCM", iv: b }, A, O);
    return new TextDecoder().decode(V);
  }
  async function v(u) {
    let d = new TextEncoder("utf-8").encode(u), p = await crypto.subtle.digest("SHA-256", d);
    return Array.from(new Uint8Array(p)).map((g) => g.toString(16).padStart(2, "0")).join("");
  }
  async function D() {
    let u = await window.crypto.subtle.generateKey({ name: "RSA-OAEP", modulusLength: 2048, publicExponent: new Uint8Array([1, 0, 1]), hash: { name: "SHA-256" } }, true, ["encrypt", "decrypt"]);
    return u.privateKeyString = e(await window.crypto.subtle.exportKey("pkcs8", u.privateKey)), u.publicKeyString = e(await window.crypto.subtle.exportKey("spki", u.publicKey)), u;
  }
  async function E(u, d) {
    typeof u == "string" && (u = await window.crypto.subtle.importKey("spki", r(u), { name: "RSA-OAEP", hash: { name: "SHA-256" } }, true, ["encrypt"]));
    let p = new TextEncoder().encode(d), g = await window.crypto.subtle.encrypt({ name: "RSA-OAEP" }, u, p);
    return e(g);
  }
  async function C(u, d) {
    typeof d == "string" && (d = r(d)), typeof u == "string" && (u = await window.crypto.subtle.importKey("pkcs8", r(u), { name: "RSA-OAEP", hash: { name: "SHA-256" } }, true, ["decrypt"]));
    let p = await window.crypto.subtle.decrypt({ name: "RSA-OAEP" }, u, d);
    return new TextDecoder().decode(p);
  }
  async function I() {
    let u = await window.crypto.subtle.generateKey({ name: "RSA-PSS", modulusLength: 2048, publicExponent: new Uint8Array([1, 0, 1]), hash: { name: "SHA-256" } }, true, ["sign", "verify"]);
    return u.privateKeyString = e(await window.crypto.subtle.exportKey("pkcs8", u.privateKey)), u.publicKeyString = e(await window.crypto.subtle.exportKey("spki", u.publicKey)), u;
  }
  async function P(u, d) {
    typeof u == "string" && (u = await window.crypto.subtle.importKey("pkcs8", r(u), { name: "RSA-PSS", hash: { name: "SHA-256" } }, true, ["sign"]));
    let p = new TextEncoder().encode(d), g = await window.crypto.subtle.sign({ name: "RSA-PSS", saltLength: 32 }, u, p);
    return e(g);
  }
  async function L(u, d, p) {
    typeof d == "string" && (d = r(d)), typeof u == "string" && (u = await window.crypto.subtle.importKey("spki", r(u), { name: "RSA-PSS", hash: { name: "SHA-256" } }, true, ["verify"]));
    let g = new TextEncoder().encode(p);
    return await window.crypto.subtle.verify({ name: "RSA-PSS", saltLength: 32 }, u, d, g);
  }
  function K(u, d = "&") {
    let p = [], g = "", x = [], y = 0, b = u.length;
    function O(A, V) {
      let z = 0, N = V - 1;
      for (; N >= 0 && A[N] === "\\"; ) z += 1, N -= 1;
      return z % 2 === 1;
    }
    for (; y < b; ) {
      let A = u[y];
      if (A === "\\") {
        g += A, y + 1 < b && (y += 1, g += u[y]), y += 1;
        continue;
      }
      if (A === '"' || A === "'" || A === "{" || A === "[" || A === "}" || A === "]") {
        A === '"' || A === "'" ? x.length && x[x.length - 1] === A ? x.pop() : x.push(A) : A === "{" || A === "[" ? x.push(A) : (A === "}" || A === "]") && x.length && (A === "}" && x[x.length - 1] === "{" || A === "]" && x[x.length - 1] === "[") && x.pop(), g += A, y += 1;
        continue;
      }
      if (A === d && x.length === 0) {
        let V = y > 0 ? u[y - 1] : "";
        if (!["'", '"', "\\"].includes(V) || O(u, y - 1)) {
          g && (p.push(g), g = ""), y += 1;
          continue;
        }
      }
      g += A, y += 1;
    }
    return g && p.push(g), p;
  }
  function X(u) {
    let d = [], p = "{", g = "}", x = 0, y = "", b = [0, 0], O = 0;
    for (let A = 0; A < u.length; A++) {
      let V = u[A], z = u[A + 1];
      if (V === p && z === p && b.every((N) => N == 0) && O == 0) {
        x === 0 && y && (d.push({ text: y.trim(), is_text: true }), y = ""), x++, A++;
        continue;
      }
      if (V === g && z === g && b.every((N) => N == 0) && O == 0) {
        x--, A++, x === 0 && (d.push({ text: y.trim(), is_text: false }), y = "");
        continue;
      }
      (V === "'" || V === '"') && (V === "'" && (b[0] == 0 ? b[0] += 1 : b[0] += -1), V === '"' && (b[1] == 0 ? b[1] += 1 : b[1] += -1)), V == "{" && b.every((N) => N == 0) && (O += 1), V == "}" && b.every((N) => N == 0) && (O += -1), y += V;
    }
    return y.trim() && d.push({ text: y.trim(), is_text: x === 0 }), d;
  }
  async function M(u, d, p) {
    let { method: g = "POST", headers: x = { "Content-Type": "application/json" }, cache: y = "no-cache", credentials: b = "include", mode: O = "cors", redirect: A = "follow", referrerPolicy: V = "no-referrer", referrer: z = location.href, urlBase: N = location.origin, res: Z = { ok: (G) => {
    }, error: (G) => {
    }, end: (G) => {
    } } } = p ?? {}, q, oe = Z?.ok, le = Z?.error, j = Z?.end, W = x?.["Content-Type"]?.includes("application/json") ? JSON.stringify(d) : d;
    typeof oe != "function" && (oe = (G) => {
    }), typeof le != "function" && (le = (G) => {
    }), typeof j != "function" && (j = (G) => {
    });
    let ge = new URL(u, N).toString();
    try {
      let G = new Request(ge, { method: g, headers: x, cache: y, credentials: b, mode: O, redirect: A, referrerPolicy: V, referrer: z, body: W }), De = await (window?.$OsApp?.http?.fetch ?? window.fetch)(G), kt = De.status;
      q = await De.json(), kt != 200 && (q = { ...q, status_code: De.status });
    } catch (G) {
      q = { status_code: 500, error: { message: G.message } };
    } finally {
      q.status_code != 200 ? le(q.error) : oe(q.data), j(q.status_code);
    }
  }
  function m(u, d = () => {
  }) {
    let p = performance.now(), g = () => {
      let y = new Function(`try{ return typeof ${u}==="undefined" ? null:${u}}catch(ex){return null;}`)();
      if (y) d(y);
      else {
        if (performance.now() - p > 6e3) return;
        setTimeout(() => {
          g();
        }, 10);
      }
    };
    g();
  }
  function k(u, d = ",") {
    let p = d == "," ? "." : ",";
    u += "";
    let g = u.split(p), x = g[0], y = g.length > 1 ? p + g[1] : "", b = /(\d+)(\d{3})/;
    for (; b.test(x); ) x = x.replace(b, "$1" + d + "$2");
    return x + y;
  }
  function w(u, d, p = false) {
    let g;
    return function() {
      let x = this, y = arguments, b = function() {
        g = null, p || u.apply(x, y);
      }, O = p && !g;
      clearTimeout(g), g = setTimeout(b, d), O && u.apply(x, y);
    };
  }
  function S(u, d) {
    let p = 0;
    return function() {
      let g = this, x = arguments, y = Date.now();
      y - p >= d && (u.apply(g, x), p = y);
    };
  }
  function T(u, d = 3, p = 1e3) {
    return u().catch((g) => {
      if (d > 0) return new Promise((x) => {
        setTimeout(() => {
          x(T(u, d - 1, p));
        }, p);
      });
      throw new Error("All retry attempts failed");
    });
  }
  return re.prototype.export = async function() {
    let u = this.bitArray.buffer, d = await l(u);
    return this.size + "&" + this.hashCount + "&" + d;
  }, Object.defineProperty(re, "import", { value: async function(u) {
    let d = new re(0, 0), p = u.split("&");
    return p.length < 3 || (d.size = parseInt(p[0]), d.hashCount = parseInt(p[1]), d.bitArray = await c(p[2])), d;
  }, writable: false, configurable: true }), { compressString: l, decompressString: h, encryptAesGcm: f, decryptAesGcm: _, sha256: v, generateKeyPairEnDecrypt: D, rsaEncode: E, rsaDeCode: C, generateKeyPairSignVerify: I, signData: P, verifySignature: L, splitString: K, fetchData: M, cbor: vt, validate: St, loadObject: m, splitString2: X, formatNumber: k, debounce: w, throttle: S, Snowflake: _t.Snowflake, promiseWithRetry: T, BloomFilter: re };
})();
var xr = (n, ...t) => {
  Ke.storeCss(document, "tfl"), F.append(n, t);
};
var br = { ...F, renderRoot: xr, css: Ke, ui: wt, utils: Et };
var Gr = new Proxy(br, { get: (n, t) => {
  if (n[t]) return n[t];
  {
    let e = t.split("").map((r) => {
      let i = r.charCodeAt(0);
      return i >= 65 && i <= 90 ? r.toLowerCase() + "-" : r;
    }).join("");
    return F.tags[e];
  }
}, ownKeys: (n) => /* @__PURE__ */ new Set([...Reflect.ownKeys(n), ...Reflect.ownKeys(n.tags)]), getOwnPropertyDescriptor: (n, t) => Reflect.getOwnPropertyDescriptor(n, t) ?? Reflect.getOwnPropertyDescriptor(n.tags, t) });

// my-form.jsx
var MyForm = ({ copy: copy2, codeConnect: codeConnect2, close: close2, showLog: showLog2, exit }) => {
  const { svg, path } = Gr.tags("http://www.w3.org/2000/svg");
  let d = {
    vi: {
      l0: "\u0110ang kh\u1EDFi t\u1EA1o d\u1EEF li\u1EC7u. Vui l\xF2ng ch\u1EDD trong \xEDt ph\xFAt.",
      l1: "M\xE3 li\xEAn k\u1EBFt",
      l2: "H\u01B0\u1EDBng d\u1EABn c\xE0i \u0111\u1EB7t",
      l3: "Sao ch\xE9p",
      l3b: "Sao ch\xE9p m\xE3 li\xEAn k\u1EBFt",
      l4: "T\xF9y ch\u1ECDn",
      l5: "Kh\u1EDFi \u0111\u1ED9ng c\xF9ng Windows",
      l6: "B\u1EADt h\u1ED9p tho\u1EA1i n\xE0y khi kh\u1EDFi \u0111\u1ED9ng",
      l7: "L\u01B0u \xFD:",
      l8: "Vui l\xF2ng KH\xD4NG \u0111\u1EB7t thi\u1EBFt l\u1EADp Standby \u0111\u1EC3 \u0111\u1EA3m b\u1EA3o h\u1EC7 th\u1ED1ng ho\u1EA1t \u0111\u1ED9ng th\xF4ng su\u1ED1t.",
      l9: "Phi\xEAn b\u1EA3n: DTM1.1.2 c\u1EADp nh\u1EADt ng\xE0y",
      l10: "D\u1EEBng",
      l11: "\u0110\xF3ng",
      l12: "Log ho\u1EA1t \u0111\u1ED9ng",
      l13: "B\u1EA1n c\xF3 mu\u1ED1n tho\xE1t ch\u01B0\u01A1ng tr\xECnh n\xE0y kh\xF4ng?",
      l14: "\u0110\u1ED3ng \xFD",
      l15: "Kh\xF4ng"
    },
    en: {
      l0: "Connection code",
      l1: "Connection code",
      l2: "Download Installation Guide",
      l3: "Copy conection code",
      l3b: "Copy to clipboard",
      l4: "Options",
      l5: "Start with Windows",
      l6: "Enable this dialog on startup",
      l7: "Note:",
      l8: "Please DO NOT set the system to Standby mode to ensure smooth operation.",
      l9: "Version: DTM1.1.2 updated on",
      l10: "Exist",
      l11: "Close",
      l12: "View activity log",
      l13: "Are you sure to exis this program?",
      l14: "Yes",
      l15: "No"
    }
  };
  let language = Gr.stateValue(d.vi);
  let changeL = (id) => {
    language.value = d[id];
  };
  function exitForm2() {
    var d2 = Dialog({ content: language.value.l13, labelYes: language.value.l14, labelClose: language.value.l15, exit });
    Gr.append(document.body, d2);
  }
  changeL("vi");
  return /* @__PURE__ */ Gr.h("div", { class: "form-container w100% dF fxdC fx1" }, () => codeConnect2.value ? null : /* @__PURE__ */ Gr.h(Dialog, { content: () => language.value.l0 }), /* @__PURE__ */ Gr.h("div", { class: "header dF aiC jc{space-between} p{8px;32px} bgc{#EEEEEE} h47px" }, /* @__PURE__ */ Gr.h("h1", { class: "2fs24px fw700" }, "D-Token Tool"), /* @__PURE__ */ Gr.h(
    "button",
    {
      class: "close-btn bdN fs30px bgTransparent crPointer",
      onClick: () => {
        typeof close2 == "function" && close2();
      }
    },
    " \xD7 "
  )), /* @__PURE__ */ Gr.h(
    "div",
    {
      className: "body bgcWhite p{0;32px;24px} dB@;label \n       taR@;#copy \n      2mt24px@;>div:first-child 2mb24px@;>div:not(:last-child) \n      dF&aiC&jcSp@;.sp \n      dF fxd{column} jc{center} fx1"
    },
    /* @__PURE__ */ Gr.h("div", null, /* @__PURE__ */ Gr.h("label", { for: "link", class: "vn mb8px" }, () => language.value.l1), /* @__PURE__ */ Gr.h("div", { className: "posR" }, /* @__PURE__ */ Gr.h("textarea", { disabled: true, id: "cc", class: "rsz{none} h{fit-content} w100% dB 2p{10px;24px;10px;16px} bd{1px;solid;#C9C9CF} bdra5px bxs{border-box} resize{none}" }, codeConnect2), /* @__PURE__ */ Gr.h("button", { onClick: (evt) => copy2(evt), className: "bdN bgc{transparent} posA t10px r0 crPointer" }, () => svg({ height: "22px", viewBox: "0 -960 960 960", width: "22px", fill: "#303030" }, path({ d: "M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z" }))))),
    /* @__PURE__ */ Gr.h("div", { class: "sp 2jcSb {#1062E5}@;i" }, /* @__PURE__ */ Gr.h("a", { href: "" }, /* @__PURE__ */ Gr.h("i", { class: "2c{#1062E5} td{none}" }, () => language.value.l2))),
    /* @__PURE__ */ Gr.h("div", { class: "sp posR 2jcSb" }, /* @__PURE__ */ Gr.h("label", { for: "language", id: "options" }, () => language.value.l4), /* @__PURE__ */ Gr.h(
      "select",
      {
        name: "language",
        onChange: (evt) => changeL(evt.target.value),
        class: "bd{2px;solid;#C9C9CF} 2px16px bdra5px h40px lh40px w144px apN outline{none} appearance{none}"
      },
      /* @__PURE__ */ Gr.h("option", { value: "vi", selected: true }, "Ti\u1EBFng Vi\u1EC7t"),
      /* @__PURE__ */ Gr.h("option", { value: "en" }, "English")
    ), /* @__PURE__ */ Gr.h("div", { class: "posA r10px t10px" }, () => svg({ height: "24px", viewBox: "0 0 24 24", width: "24px", fill: "black" }, path({ d: "M7 10l5 5 5-5z" })))),
    /* @__PURE__ */ Gr.h(
      "div",
      {
        class: "bdra4px&w20px&h20px&bd{1.9px;solid;#303030}&bgc{transparent}&ap{none}&dIb&cr&mr11.5px@;input[type='checkbox'] bd{1.9px;solid;#303030}&posR&accent-color{#303030}@;input[type='checkbox']:checked mb8px@;label:first-child"
      },
      /* @__PURE__ */ Gr.h("label", { className: "dF aiC" }, /* @__PURE__ */ Gr.h("input", { type: "checkbox", checked: true }), /* @__PURE__ */ Gr.h("span", null, () => language.value.l5)),
      /* @__PURE__ */ Gr.h("label", { className: "dF aiC" }, /* @__PURE__ */ Gr.h("input", { type: "checkbox" }), /* @__PURE__ */ Gr.h("span", { id: "op2" }, () => language.value.l6))
    ),
    /* @__PURE__ */ Gr.h("div", null, /* @__PURE__ */ Gr.h("i", { class: "fw700 tdU fs{italic}", id: "note" }, () => language.value.l7), /* @__PURE__ */ Gr.h("span", { id: "note2" }, () => language.value.l8)),
    /* @__PURE__ */ Gr.h("div", { class: "fs13px" }, /* @__PURE__ */ Gr.h("span", { id: "version" }, () => language.value.l9), /* @__PURE__ */ Gr.h("span", null, "01/04/2025"))
  ), /* @__PURE__ */ Gr.h(
    "div",
    {
      class: "footer sp p{24px;32px} taR fs16px&w126px&taC&ws{nowrap}&h40px&bdra5px@;button  aiC dF jc{space-between}"
    },
    /* @__PURE__ */ Gr.h("button", { onClick: () => typeof showLog2 == "function" && showLog2(), class: "2bd{2px;solid;#1062E5} 2c{#1062E5} bgc{transparent} crPointer" }, () => language.value.l12),
    /* @__PURE__ */ Gr.h("div", { className: "ml24px@;button:last-child" }, /* @__PURE__ */ Gr.h(
      "button",
      {
        class: "2bd{2px;solid;#1062E5} 2c{#1062E5} bgc{transparent} crPointer",
        onClick: () => exitForm2()
      },
      () => language.value.l10
    ), /* @__PURE__ */ Gr.h(
      "button",
      {
        class: "2bgc{#1062E5} bdN 2c{#FFFFFF} crPointer",
        onClick: close2
      },
      () => language.value.l11
    ))
  ));
};

// components/tooltip.jsx
var Tooltip = () => {
  var el = /* @__PURE__ */ Gr.h("div", { className: `posF t50% l50% tf{translate(-50%)} w150px py5px bgc{#303030} c{#fff} taC bdra5px` }, "Copy to clipboard");
  setTimeout(() => {
    el.remove();
  }, 1e3);
  Gr.append(document.body, el);
};

// index.jsx
window.$ = Gr;
var codeConnect = Gr.stateValue("");
function copy(event) {
  Tooltip({ event });
}
function close() {
}
function showLog() {
  var d = Dialog({ content: "abc", close: true });
  Gr.append(document.body, d);
}
function exitForm() {
  console.log("close");
}
var app = document.getElementById("app");
Gr.renderRoot(app, MyForm({ copy, codeConnect, close, showLog, exitForm }));
setTimeout(() => {
  codeConnect.value = "SBIihMBeql1QLCvu5bt7Qp9JMG4SB6ADRxGGZmSB25la3Bz3gKBW8bTW7jI7ZtcGfuB6";
}, 5e3);
