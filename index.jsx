import { Dialog } from './components/dialog.jsx';
import $ from './dw.js';
import {MyForm} from './my-form.jsx';
import { Tooltip } from './components/tooltip';
window.$=$;

const codeConnect = $.stateValue('')
function copy(event){
  // navigator.clipboard.writeText(codeConnect.value);
  Tooltip({event});
 
}
function close(){}

function showLog(){
  var d = Dialog({content:'abc', close: true});
  $.append(document.body, d);
}

function exitForm(){
  console.log("close")
}

const app = document.getElementById('app');
$.renderRoot(app, MyForm({copy,codeConnect, close, showLog, exitForm}))
setTimeout(()=>{
  codeConnect.value= 'SBIihMBeql1QLCvu5bt7Qp9JMG4SB6ADRxGGZmSB25la3Bz3gKBW8bTW7jI7ZtcGfuB6'
},5000)