
export const Dialog = (props) => {
  function close(){
    el.remove()
  }

  let el = <div className='posF z999 bgc{rgba(48,;48,;48,;0.2)} h100vh w100vw t0 dF aiC jcC'>
      <div className='bdra20px bgcWhite p32px'>
        <div class="maxh88vh ovSc">{props.content}</div>
        <div className='dF jcE'>
      <div className='AA'>
        {
          ()=>props?.labelClose ? <button className='bdN bgcTransparent flR c#1062E5 fs16px mt34px' onClick={() => close()}>{props.labelClose}</button> : null
        }
      </div>
      <div className='BB'>
        {
          ()=>{ 
            return props?.labelYes ? <button className='bdN bgcTransparent flR c#1062E5 fs16px mt34px' onClick={()=>{
              close();
              typeof props.exit == 'function' && props.exit();
            }}>{props.labelYes}</button> : null}
        }
      </div>
      </div>
       
      </div>
  </div>

return el;
}