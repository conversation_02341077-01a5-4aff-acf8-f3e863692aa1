import $ from './dw';
import { Dialog } from './components/dialog.jsx';

export const MyForm = ({ copy, codeConnect, close, showLog, exit }) => {
  const { svg, path } = $.tags("http://www.w3.org/2000/svg");

  let d = {
    vi: {
      l0: 'Đang khởi tạo dữ liệu. Vui lòng chờ trong ít phút.',
      l1: 'Mã liên kết',
      l2: 'Hướng dẫn cài đặt',
      l3: 'Sao chép',
      l3b: 'Sao chép mã liên kết',
      l4: 'Tùy chọn',
      l5: 'Khởi động cùng Windows',
      l6: '<PERSON><PERSON>t hộp thoại này khi khởi động',
      l7: 'Lưu ý:',
      l8: 'Vui lòng KHÔNG đặt thiết lập Standby để đảm bảo hệ thống hoạt động thông suốt.',
      l9: '<PERSON>ên bản: DTM1.1.2 cập nhật ngày',
      l10: 'Dừng',
      l11: 'Đóng',
      l12: 'Log hoạt động',
      l13: '<PERSON><PERSON>n có muốn thoát chương trình này không?',
      l14: 'Đồng ý',
      l15: 'Không',
    },
    en: {
      l0: 'Connection code',
      l1: 'Connection code',
      l2: 'Download Installation Guide',
      l3: 'Copy conection code',
      l3b: 'Copy to clipboard',
      l4: 'Options',
      l5: 'Start with Windows',
      l6: 'Enable this dialog on startup',
      l7: 'Note:',
      l8: 'Please DO NOT set the system to Standby mode to ensure smooth operation.',
      l9: 'Version: DTM1.1.2 updated on',
      l10: 'Exist',
      l11: 'Close',
      l12: 'View activity log',
      l13: 'Are you sure to exis this program?',
      l14: 'Yes',
      l15: 'No',
    }
  };

  let language = $.stateValue(d.vi);
  let changeL = (id) => {
    language.value = d[id];
  }
  function exitForm() {
    var d = Dialog({ content: language.value.l13, labelYes: language.value.l14, labelClose: language.value.l15, exit });
    $.append(document.body, d);
  }

  changeL('vi')

  return <div class="form-container w100% dF fxdC fx1">
    {() => codeConnect.value ? null : (
      <Dialog content={() => language.value.l0} />
    )}

    <div class="header dF aiC jc{space-between} p{8px;32px} bgc{#EEEEEE} h47px">
      <h1 class="2fs24px fw700">D-Token Tool</h1>
      <button class="close-btn bdN fs30px bgTransparent crPointer"
        onClick={() => {
          typeof close == 'function' && close();
        }}
      > &times; </button>
    </div>
    <div
      className="body bgcWhite p{0;32px;24px} dB@;label 
       taR@;#copy 
      2mt24px@;>div:first-child 2mb24px@;>div:not(:last-child) 
      dF&aiC&jcSp@;.sp 
      dF fxd{column} jc{center} fx1">
      <div>
        <label for="link" class="vn mb8px">{() => language.value.l1}</label>
        <div className='posR'>
          <textarea disabled id="cc" class="rsz{none} h{fit-content} w100% dB 2p{10px;24px;10px;16px} bd{1px;solid;#C9C9CF} bdra5px bxs{border-box} resize{none}"  >{codeConnect}</textarea>
          <button onClick={(evt) => copy(evt)} className='bdN bgc{transparent} posA t10px r0 crPointer'>
            {
              () => svg({ height: "22px", viewBox: "0 -960 960 960", width: "22px", fill: "#303030" }, path({ d: "M360-240q-33 0-56.5-23.5T280-320v-480q0-33 23.5-56.5T360-880h360q33 0 56.5 23.5T800-800v480q0 33-23.5 56.5T720-240H360Zm0-80h360v-480H360v480ZM200-80q-33 0-56.5-23.5T120-160v-560h80v560h440v80H200Zm160-240v-480 480Z" }))
            }
            {/* {() => language.value.l3} */}
          </button>
        </div>
      </div>
      <div class="sp 2jcSb {#1062E5}@;i">
        <a href=""><i class="2c{#1062E5} td{none}">{() => language.value.l2}</i></a>
      </div>
      <div class="sp posR 2jcSb">
        <label for="language" id="options">{() => language.value.l4}</label>
        <select name="language" onChange={(evt) => changeL(evt.target.value)}
          class="bd{2px;solid;#C9C9CF} 2px16px bdra5px h40px lh40px w144px apN outline{none} appearance{none}">
          <option value="vi" selected>Tiếng Việt</option>
          <option value="en">English</option>
        </select>
        <div class="posA r10px t10px">
          {
            () => svg({ height: "24px", viewBox: "0 0 24 24", width: "24px", fill: "black" }, path({ d: "M7 10l5 5 5-5z" }))
          }
        </div>
      </div>
      <div
        class="bdra4px&w20px&h20px&bd{1.9px;solid;#303030}&bgc{transparent}&ap{none}&dIb&cr&mr11.5px@;input[type='checkbox'] bd{1.9px;solid;#303030}&posR&accent-color{#303030}@;input[type='checkbox']:checked mb8px@;label:first-child">
        <label className="dF aiC">
          <input type="checkbox" checked />
          <span>{() => language.value.l5}</span>
        </label>
        <label className="dF aiC">
          <input type="checkbox" />
          <span id="op2">{() => language.value.l6}</span>
        </label>
      </div>
      <div>
        <i class="fw700 tdU fs{italic}" id="note">{() => language.value.l7}</i>
        <span id="note2">{() => language.value.l8}</span>
      </div>
      <div class="fs13px"><span id="version">{() => language.value.l9}</span><span>01/04/2025</span></div>
    </div>
    <div
      class="footer sp p{24px;32px} taR fs16px&w126px&taC&ws{nowrap}&h40px&bdra5px@;button  aiC dF jc{space-between}">
      <button onClick={() => typeof showLog == 'function' && showLog()} class="2bd{2px;solid;#1062E5} 2c{#1062E5} bgc{transparent} crPointer">{() => language.value.l12}</button>
      <div className='ml24px@;button:last-child'>
        <button class="2bd{2px;solid;#1062E5} 2c{#1062E5} bgc{transparent} crPointer"
          onClick={() => exitForm()}
        >{() => language.value.l10}</button>
        <button class="2bgc{#1062E5} bdN 2c{#FFFFFF} crPointer"
          onClick={close}
        >{() => language.value.l11}</button>
      </div>
    </div>
  </div>
}